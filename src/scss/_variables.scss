// Fonts
$primary-font-family: '<PERSON> Sans', '<PERSON> Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;

// Font Size
$font-size-1: 12px;
$font-size-2: 16px;
$font-size-3: 18px;
$font-size-4: 20px;
$font-size-5: 24px;

// Font Weight for Amazon Ember font (Using SCSS Maps)
$font-weight: (
  'thin': 300,
  'regular': 400,
  'light': 500,
  'medium': 600,
  'bold': 700,
  'heavy': 800
);

// Colors
$primary-dark: #131921;

$primary-yellow: #FFD814;
$lighten-yellow: #ffe180;

$primary-red: #cf0707;
$error-red-bg: #FF9494;
$error-red: #f00000;

$primary-blue: #0091cf;
$lighten-blue: #00b3ff;
$sky-light-blue: #a4e4ff;
$sky-lighter-blue: #d4f4ff;

$primary-dark-blue: #232F3E;

$primary-green: #2E9F1C;

$primary-dark-text-color: #333;
$primary-lighter-text-color: #666;

$info-bg: #bedeff;
$info-text: #084298;

$warning-bg: #ffe180;
$warning-text: #534000;

$error-bg: #ffb2b9;
$error-text: #580007;

$success-bg: #9fffa3;
$success-text: #002e02;


// Box shadow
$box-shadow-1: 0px 0px 0px 1px #0000000d,
  0px 0px 0px 1px inset #d1d5db;
$box-shadow-2: 0px 0px 0px 5px #0000000d,
  0px 0px 0px 2px inset #d1d5db;
$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px,
  #0091cfa6 0px 4px 6px -1px;

// Border radius
$border-radius-1: 3px;
$border-radius-2: 5px;
$border-radius-3: 8px;
$border-radius-4: 10px;

// Padding
$padding-1: 5px;
$padding-2: 10px;
$padding-3: 12px;
$padding-4: 15px;
$padding-5: 20px;

// Media queries
$mobile: 576px;
$tablet: 768px;
$laptop: 992px;
$monitor: 1200px;