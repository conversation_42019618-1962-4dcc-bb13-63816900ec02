@use '../../../src/scss/variables' as *;
@use '../../../src/scss/mixins' as *;


.my_account {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;

  .navbar {
    // background-color: $sky-lighter-blue;

    h3 {
      padding: 0.5rem 0;
      font-size: 23px;
      text-align: center;
      font-weight: bold;
    }

    .navbar__links {
      width: 100%;
      @include flexbox(center, center); // Center all links horizontally

      .navbar__links__links {
        @include flexbox(center, center, column); // Center links in a vertical column

        a {
          @include flexbox(flex-start, center, row); // Align icon and text
          column-gap: 10px; // Space between icon and text
          padding: 0.9rem 1rem;
          width: 100%; // Full width for consistent layout
          color: $primary-blue;
          font-weight: bold;
          font-size: 17px;
          letter-spacing: 0.4px;

          &:hover {
            text-decoration: underline;
          }

          i {
            font-size: 20px; // Slightly larger icon for clarity
          }
        }
      }
    }
  }

  .content {
    flex: 1;
  }
}

@media (min-width: $tablet) {
  .my_account {
    .navbar {
      min-width: 250px;
    }
  }
}