import { useForm, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import loading_svg from '../../../../assets/loading_svg_blue.svg'
import { AxiosError } from 'axios'
import styles from './ChangeAuthInfo.module.scss'
import { useRouter } from 'next/navigation'

export type VerifyCodeShape = z.infer<typeof verificationCodeSchema>

const VerifyAuthInfo = () => {
  const router = useRouter()
  const { register, handleSubmit, formState: { errors } } = useForm<VerifyCodeShape>({
    resolver: zodResolver(verificationCodeSchema),
  })

  const { mutation } = useSendVerifyCode()

  const onSubmit: SubmitHandler<VerifyCodeShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: () => {
        router.push('/customer')
      },
    })
  }

  return (
    <div className={styles.register_container}>
      <div className={styles.form_container}>
        <div className='logo_header'>
          <Logo />
        </div>
        {/* <h2 className='title'>Verify Contact Information</h2> */}

        {mutation.error && (
          <Alert
            variant="error"
            message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)}
            textAlign='center'
          />
        )}

        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <Alert
            variant="success"
            message={`Enter the received verification code here.`}
          // textAlign='center'
          />

          <div className='form_group'>
            <label className='form_label'>Enter the verification code:</label>
            {/* <input
              className='form_input'
              type='number'
              {...register("code")}
              disabled={mutation.isPending}
            /> */}
            <input
              className='form_input'
              type='text'
              inputMode='numeric'
              id='verification_code'
              disabled={mutation.isPending}
              onInput={(e) => {
                e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '')
              }}
              {...register("code")}
            // style={{ appearance: 'textfield', MozAppearance: 'textfield' }}
            />
            {errors.code && (
              <p className='form_error'>{errors.code.message}</p>
            )}
          </div>

          <button className={`empty_btn ${styles.empty_btn_2}`} type="submit" disabled={mutation.isPending}>
            {mutation.isPending ? (
              <img src={loading_svg} alt="Loading..." className='loading_svg' />
            ) : 'Submit'}
          </button>
        </form>
      </div>
    </div>
  )
}

export default VerifyAuthInfo