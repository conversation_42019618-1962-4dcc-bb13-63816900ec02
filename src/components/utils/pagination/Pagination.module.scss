@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.pagination {
  margin: 2rem 0;
  @include flexbox(center, center, row);
  gap: 10px;

  .page_button {
    @include btn($primary-dark, $sky-light-blue);
    transition: background-color 0.3s ease, color 0.3s ease;

    &:hover {
      background-color: darken($sky-light-blue, 15%);
    }

    &:disabled {
      background-color: lighten($primary-lighter-text-color, 20%);
      cursor: not-allowed;
    }

    &.active {
      background-color: $lighten-blue;
      color: $primary-dark;
      font-weight: bold;
      cursor: default;
    }
  }

  .dots {
    @include flexbox(center, center, row);
    font-size: $font-size-3;
  }

  @include mobile {
    flex-wrap: wrap;
    gap: 5px;

    .page_button {
      padding: $padding-1 $padding-2;
      font-size: $font-size-2;
    }
  }
}