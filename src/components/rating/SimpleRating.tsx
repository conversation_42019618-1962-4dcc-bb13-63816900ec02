import { FaStar, FaRegStar, FaStarHalfAlt } from "react-icons/fa"
import { ProductShape } from "../../types/product-types"
import styles from './Rating.module.scss'

interface Props {
  product: ProductShape
  color?: string
}

export const SimpleRating = ({ product }: Props) => {
  function starRating(rating: number) {
    return (
      rating < 2 ? (
        <div>
          <FaRegStar />
        </div>
      ) : rating < 4 ? (
        <div>
          <FaStarHalfAlt />
        </div>
      ) : (
        <div>
          <FaStar />
        </div>
      )
    )
  }

  return (
    <section className={styles.rating_2}>
      {product?.average_rating && product?.average_rating > 0 ? (
        <div>
          <i>{starRating(product?.average_rating)}</i>
          {/* <p>234 sold</p> */}
          {/* <p>({product.average_rating})</p> */}
          <p>({product?.reviews?.length} reviews) </p>
        </div>
      ) : (
        <p>(No reviews yet)</p>
      )}
    </section>
  )
};


