import { ReactNode } from 'react'
import { PayPalScriptProvider } from "@paypal/react-paypal-js"


interface Props {
  children: ReactNode
}

const PayPalProvider = ({ children }: Props) => {
  return (
    <PayPalScriptProvider
      options={{
        "clientId": process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID,
        currency: "USD",
        intent: "capture"
      }}
    >
      {children}
    </PayPalScriptProvider>
  )
}


export default PayPalProvider
