import { useState } from "react"
import { FaChevronLeft, FaChevronRight } from "react-icons/fa"
import { ProductShape, ProductVariant } from "../../../../types/product-types"
import styles from "./ProductDetailsImg.module.scss"
import noImagePlaceholder from "../../../../assets/no-image-placeholder.png"

interface Props {
  product: ProductShape
  selectedImage: string
  handleImageClick: (image: string) => void
  selectedVariant?: ProductVariant
}

interface ImageItem {
  id: number
  url: string
  alt: string
}

const ProductDetailsImg = ({ product, selectedImage, handleImageClick, selectedVariant }: Props) => {
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 })
  const [isZoomed, setIsZoomed] = useState(false)

  // Collect all product images, prioritizing selected variant's images by their order
  const allImages: ImageItem[] = []
  const seenUrls = new Set<string>()

  // Add images from selected variant first (sorted by order)
  if (selectedVariant && selectedVariant.product_image?.length) {
    const sortedSelectedImages = [...selectedVariant.product_image].sort((a, b) => a.order - b.order)
    sortedSelectedImages.forEach(image => {
      const imageUrl = `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${image.image}`
      if (!seenUrls.has(imageUrl)) {
        allImages.push({
          id: image.id,
          url: imageUrl,
          alt: image.alternative_text
        })
        seenUrls.add(imageUrl)
      }
    })
  }

  // Add images from other variants (sorted by order), skipping duplicates
  const otherVariants = product?.product_variant?.filter(v => v.id !== selectedVariant?.id) || []
  const sortedOtherVariants = [...otherVariants].sort((a, b) => a.order - b.order)
  sortedOtherVariants.forEach(variant => {
    if (variant.product_image?.length) {
      const sortedImages = [...variant.product_image].sort((a, b) => a.order - b.order)
      sortedImages.forEach(image => {
        const imageUrl = `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${image.image}`
        if (!seenUrls.has(imageUrl)) {
          allImages.push({
            id: image.id,
            url: imageUrl,
            alt: image.alternative_text
          })
          seenUrls.add(imageUrl)
        }
      })
    }
  })

  // Add a default placeholder image if no images are found
  if (allImages.length === 0) {
    allImages.push({
      id: 0,
      url: noImagePlaceholder,
      alt: "No image available"
    })
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isZoomed) return

    const { left, top, width, height } = e.currentTarget.getBoundingClientRect()
    const x = ((e.clientX - left) / width) * 100
    const y = ((e.clientY - top) / height) * 100

    setHoverPosition({ x, y })
  }

  const navigateImage = (direction: 'prev' | 'next') => {
    const currentIndex = allImages.findIndex(img => img.url === selectedImage)
    if (currentIndex === -1) return

    let newIndex
    if (direction === 'prev') {
      newIndex = (currentIndex - 1 + allImages.length) % allImages.length
    } else {
      newIndex = (currentIndex + 1) % allImages.length
    }

    handleImageClick(allImages[newIndex].url)
  }

  return (
    <section className={styles.product_images}>
      <div className={styles.product_images__list}>
        {allImages.map((image) => (
          <div
            key={image.id}
            className={`${styles.thumbnail} ${selectedImage === image.url ? styles.active : ''}`}
            onClick={() => handleImageClick(image.url)}
          >
            <img src={image.url} alt={image.alt} />
          </div>
        ))}
      </div>

      <div className={styles.product_image}>
        <div
          className={styles.image_container}
          onMouseEnter={() => setIsZoomed(true)}
          onMouseLeave={() => setIsZoomed(false)}
          onMouseMove={handleMouseMove}
          style={{ cursor: isZoomed ? 'zoom-in' : 'default' }}
        >
          <img
            src={selectedImage}
            alt={allImages.find(img => img.url === selectedImage)?.alt || "Product image"}
            style={isZoomed ? {
              transform: `scale(2)`,
              transformOrigin: `${hoverPosition.x}% ${hoverPosition.y}%`
            } : undefined}
          />

          {allImages.length > 1 && (
            <div className={styles.image_navigation}>
              <button
                className={styles.nav_button}
                onClick={() => navigateImage('prev')}
                aria-label="Previous image"
              >
                <FaChevronLeft />
              </button>
              <button
                className={styles.nav_button}
                onClick={() => navigateImage('next')}
                aria-label="Next image"
              >
                <FaChevronRight />
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}

export default ProductDetailsImg