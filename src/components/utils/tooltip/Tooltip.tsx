import React, { ReactNode } from 'react'
import styles from './Tooltip.module.scss'

interface TooltipProps {
  children: ReactNode
  content: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  disabled?: boolean
  className?: string
  showOnHover?: boolean
  showOnCondition?: boolean
}

const Tooltip: React.FC<TooltipProps> = ({
  children,
  content,
  position = 'top',
  disabled = false,
  className = '',
  showOnHover = true,
  showOnCondition = false
}) => {
  // Don't show tooltip if disabled or no content
  if (disabled || !content) {
    return <>{children}</>
  }

  const tooltipClasses = [
    styles.tooltip,
    styles[`tooltip--${position}`],
    showOnHover ? styles['tooltip--hover'] : '',
    showOnCondition ? styles['tooltip--condition'] : '',
    className
  ].filter(Boolean).join(' ')

  return (
    <div className={tooltipClasses} data-tooltip={content}>
      {children}
    </div>
  )
}

export default Tooltip
