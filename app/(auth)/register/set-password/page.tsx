'use client'

import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { useRouter } from 'next/navigation'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { setPasswordSchema } from '@/src/schemas/schemas'
import Underlay from '@/src/components/utils/underlay/Underlay'

import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '@/src/types/types'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import authStore from '@/src/stores/auth-store'
import { useEffect } from 'react'
import styles from './SetPassword.module.scss'
import { useSetPassword } from '@/src/hooks/auth-hooks'
import { useTogglePasswordVisibility } from '@/src/hooks/other-hooks'
import RegisterLayout from '../RegisterLayout'

export type passwordSchemaFormInputs = z.infer<typeof setPasswordSchema>

const SetPassword = () => {
  const router = useRouter()
  const { mutation } = useSetPassword()
  const { username, regInitiated, passwordSubmitted, setIsLoggedIn, setPasswordSubmitted } = authStore()

  const { isVisible: isNewPasswordVisible, toggleVisibility: toggleNewPasswordVisibility } = useTogglePasswordVisibility()
  const { isVisible: isReNewPasswordVisible, toggleVisibility: toggleReNewPasswordVisibility } = useTogglePasswordVisibility()

  const { register, handleSubmit, formState: { errors } } = useForm<passwordSchemaFormInputs>({
    resolver: zodResolver(setPasswordSchema)
  })

  console.log('PasswordSubmitted in SetPassword', passwordSubmitted)
  console.log('Username in SetPassword', username)

  const onSubmit: SubmitHandler<passwordSchemaFormInputs> = async (data) => {
    const { password, confirm_password } = data
    mutation.mutate({
      password,
      confirm_password,
    }, {
      onSuccess: () => {
        // setUsername(null)
        setPasswordSubmitted(true)
        setIsLoggedIn(true)
        router.push('/register/create-customer/')
      },
    })
  }

  useEffect(() => {
    if (passwordSubmitted) {
      router.push('/register/create-customer/')
    }
  })

  useEffect(() => {
    if (!regInitiated) {
      router.push('/register/initiate/') // or whichever page you'd like to send them to
    }
  }, [regInitiated, router])

  return (
    <Underlay isOpen>
      {!passwordSubmitted && regInitiated && (
        <RegisterLayout title="Set your password" error={mutation.error as AxiosError<ErrorResponse>}>
          <form onSubmit={handleSubmit(onSubmit)} className='form'>

            <div className='form_group'>
              <label className='form_label' htmlFor="password">Password:</label>
              <section className='password__container'>
                <input
                  className='form_input'
                  type={isNewPasswordVisible ? "text" : "password"}
                  id="password"
                  {...register('password')}
                />
                <span onClick={toggleNewPasswordVisibility}>
                  <i>{isNewPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
                </span>
              </section>
              {errors.password && <p className='form_error'>{errors.password.message} &#128543;</p>}
            </div>
            <div className='form_group'>
              <label className='form_label' htmlFor="confirm_password">Confirm Password:</label>
              <section className='password__container'>
                <input
                  className='form_input'
                  type={isReNewPasswordVisible ? "text" : "password"}
                  id="confirm_password"
                  {...register('confirm_password')}
                />
                <span onClick={toggleReNewPasswordVisibility}>
                  <i>{isReNewPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
                </span>
              </section>
              {errors.confirm_password && <p className='form_error'>{errors.confirm_password.message} &#128543;</p>}
            </div>
            <section className='btn_container'>
              {/* <button
                  type="button"
                  className='empty_btn'
                  disabled={mutation.isPending}
                  onClick={() => navigate('/customer')}>Cancel
                </button> */}
              <button type="submit" className='empty_btn' disabled={mutation.isPending}>
                {mutation.isPending ? (
                  // <img src={loading_blue} alt="Loading..." className='loading_svg' />
                  'Setting Password...'
                ) : (
                  'Set Password'
                )}
              </button>
            </section>
          </form>
        </RegisterLayout>
      )}
    </Underlay>
  )
}

export default SetPassword
