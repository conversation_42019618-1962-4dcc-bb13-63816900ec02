@use '../../scss/variables' as *;
@use '../../scss/mixins' as *;

.header {
  background-color: $primary-dark;
}

.header__top {
  display: grid;
  grid-template-columns: repeat(3, auto);
  color: #fff;
  gap: 1rem;
  padding: 10px;
  align-items: center;
}

.header__search {
  grid-column: 2 / 4;
  margin: 0 auto;
  width: 100%;
}

.header__end {
  // background-color: rgb(149, 230, 230);
  @include flexbox(flex-end, center, row);

  grid-column: 1 / 4;
  column-gap: 1rem;
  white-space: nowrap;
  padding: 0 5px;
}

.cart {
  @include flexbox(flex-start, center);

  p {
    position: absolute;
    margin: -30px 0 0 10px;
  }

  a {
    margin: 0 0 0 5px;
    color: #fff;

    &:hover {
      text-decoration: underline;
    }
  }
}

.header__sign_in {
  position: relative;

  .header__login {
    @include flexbox(flex-start, center);
    cursor: pointer;
    // padding: 6px 12px;
    border-radius: $border-radius-1;
    transition: all 0.2s ease;
    color: #fff;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);
    }

    >* {
      color: inherit;
      transition: all 0.2s ease;
    }

    i {
      font-size: $font-size-3;
      margin-left: 6px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      transition: transform 0.2s ease;
    }

    &:hover i {
      transform: translateY(1px);
    }

    /* Normalize inner text/links alignment */
    p {
      @include flexbox(flex-start, center);
      gap: 6px;
      margin: 0;
      line-height: 1;
    }
  }

  /* Modifier: when showing only Sign In / Sign Up links (logged out),
     do not make the whole area look/behave like a button */
  .header__login_links {
    cursor: default;

    &:hover {
      background-color: transparent;
      box-shadow: none;
    }

    p {
      @include flexbox(flex-start, center);
      gap: 6px;
      margin: 0;
      line-height: 1;

      a {
        text-decoration: none;
        margin: 0 2px;
        color: #fff;
        position: relative;
        display: inline-block;

        /* hover decoration handled by animated underline */

        /* Animated underline */
        &::after {
          content: '';
          position: absolute;
          left: 0;
          bottom: -2px;
          height: 1px;
          width: 100%;
          background-color: currentColor;
          transform: scaleX(0);
          transform-origin: left center;
          transition: transform 60ms ease-in-out;
        }

        &:hover::after {
          transform: scaleX(1);
        }
      }
    }
  }

  .warning_link {
    padding: 0 0 0 0;
    color: $warning-bg;


    &:hover {
      background: none;
      text-decoration: underline;
    }
  }


  .dropdown_container {
    position: absolute;
    right: -13px;
    top: calc(100% + 5px);
    width: 260px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(8px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    /* Hover bridge to prevent disappearing */
    &::after {
      content: '';
      position: absolute;
      top: -10px;
      left: 0;
      right: 0;
      height: 10px;
      background: transparent;
    }

    &.active {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .dropdown {
      background: #fff;
      border-radius: $border-radius-2;
      box-shadow: $box-shadow-2;
      border: 1px solid #d1d5db;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: -6px;
        right: 16px;
        width: 12px;
        height: 12px;
        background: #fff;
        transform: rotate(45deg);
        border-top: 1px solid #d1d5db;
        border-left: 1px solid #d1d5db;
      }

      .dropdown_header {
        padding: $padding-3 $padding-4;
        border-bottom: 1px solid #e5e7eb;
        background-color: #f9fafb;

        h4 {
          color: $primary-dark-blue;
          margin: 0;
          font-size: $font-size-2;
          font-weight: 600;
          text-align: left !important;
          line-height: 1.4;
        }
      }

      .dropdown_menu {
        padding: $padding-1 0;

        .menu_item {
          @include flexbox(flex-start, center);
          padding: 10px $padding-4;
          color: $primary-dark-text-color;
          font-size: $font-size-2;
          font-weight: 500;
          transition: all 0.15s ease;
          cursor: pointer;

          &:hover {
            background-color: $sky-lighter-blue;
            color: $primary-blue;

            i {
              color: $primary-blue;
            }
          }

          i {
            margin-right: $padding-3;
            font-size: $font-size-4;
            color: $primary-lighter-text-color;
            width: 24px;
            text-align: center;
            transition: color 0.15s ease;
          }

          a {
            color: inherit;
            text-decoration: none;
            width: 100%;
            display: flex;
            align-items: center;
          }
        }

        .divider {
          height: 1px;
          background-color: #e5e7eb;
          margin: $padding-1 0;
        }
      }

      .dropdown_footer {
        padding: $padding-2 $padding-4;
        border-top: 1px solid #e5e7eb;
        background-color: #f9fafb;

        .menu_item {
          @include flexbox(center, center);
          color: $error-red;
          font-weight: 600;
          padding: 8px 0;
          border-radius: $border-radius-1;
          transition: all 0.15s ease;

          &:hover {
            background-color: #fee2e2;
            color: darken($error-red, 10%);
          }

          i {
            margin-right: $padding-2;
            color: inherit;
          }
        }
      }
    }
  }
}

.header__bottom_nav {
  background-color: $primary-dark-blue;
}

.wishlist {
  @include flexbox(flex-start, center);

  p {
    position: absolute;
    margin: -30px 0 0 10px;
  }

  a {
    margin: 0 0 0 5px;
    color: #fff;

    &:hover {
      text-decoration: underline;
    }
  }
}

.header__icon {
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.header__badge {
  @include flexbox(center, center);
  border-radius: 50%;
  background-color: $primary-blue;
  width: 22px;
  height: 22px;

  span {
    font-size: 15px;
  }
}


@media (width > $tablet) {
  .header {
    padding: 6px 0;
  }

  .header__top {
    grid-template-columns: 200px minmax(400px, 3fr) 300px;
    width: 100%;
    margin: 0 auto;
    padding: 10px 0;
    gap: 2rem;
  }


  .header__logo {
    grid-column: 1 / 2;
  }

  .header__search {
    grid-column: 2 / 3;
  }

  .header__end {
    grid-column: 3 / 4;
  }
}