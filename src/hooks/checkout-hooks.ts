import { useMutation, useQuery } from "@tanstack/react-query"
import APIClient from "../lib/api-client"
import { useOrder } from "./order-hooks"
import { PaymentOptionsShape } from "../types/types"


interface ClientSecretShape {
  client_secret: string
}

interface CaptureOrderRequest {
  paypal_order_id: string
}

interface CaptureOrderResponse {
  status: string
  payment_status: string
}

interface PayPalOrderRequest {
  order_id: number
  amount: string
}

interface PayPalOrderResponse {
  id: string
  status: string
}


export const useClientSecret = (orderId: number) => {
  const { data: order } = useOrder(orderId)
  const apiClient = new APIClient<ClientSecretShape>(`payments/payment-intent-secret/`)

  return useQuery({
    queryKey: ['stripeClientSecret', orderId],
    queryFn: () => apiClient.get({
      params: {
        order_id: orderId
      }
    }),
    // If order is not null or undefined and payment method is Stripe, then enable the query
    enabled: !!order && order.payment_method.slug === 'stripe'
  })
}

export const usePaymentOptions = () => {
  const apiClient = new APIClient<PaymentOptionsShape[]>('/payments/payment-options/')

  const payOptions = useQuery({
    queryKey: ['payment_options'],
    queryFn: apiClient.get,
    // keepPreviousData: true,
    // refetchOnWindowFocus: false,
    staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
  })

  return payOptions
}

// PayPal Hooks
export const useCaptureOrder = () => {
  const apiClient = new APIClient<CaptureOrderResponse, CaptureOrderRequest>('/payments/capture-paypal-order/')

  return useMutation({
    mutationFn: (data: CaptureOrderRequest) => apiClient.post(data)
  })
}

export const useCreatePayPalOrder = ({ orderId, amount }: { orderId: number, amount: number }) => {
  const apiClient = new APIClient<PayPalOrderResponse, PayPalOrderRequest>('payments/create-paypal-order/')

  return useMutation({
    mutationFn: () => apiClient.post({
      order_id: orderId,
      amount: amount.toString()
    })
  })
}