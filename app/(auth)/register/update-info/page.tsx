'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import authStore from '@/src/stores/auth-store'
import UpdateAuthContact from './UpdateAuthContact'
import { VerifyAuthContact } from './VerifyAuthContact'
import RegisterLayout from '../RegisterLayout'

const UpdateAuthInfoContainer = () => {
  const router = useRouter()
  const [verificationStep, setVerificationStep] = useState(false)
  const [altUsername, setAltUsername] = useState<string | null>(null)

  const {
    username,
    // setIsLoggedIn: login,
    setRegInitiated,
    setVerificationCodeSubmitted,
    setPasswordSubmitted,
    setUpdateAuthInfoSubmitted,
    setCustomerDetailsSubmitted,
  } = authStore()

  const handleContactInfoSuccess = (username: string) => {
    setAltUsername(username)
    setVerificationStep(true)
  }


  const handleVerificationSuccess = () => {
    // login()
    setPasswordSubmitted(false)
    setRegInitiated(false)
    setVerificationCodeSubmitted(false)
    setCustomerDetailsSubmitted(false)
    setUpdateAuthInfoSubmitted(false)
    // router.push('/customer')
  }

  const handleSkip = () => {
    // router.push('/customer')
  }

  return (
    <RegisterLayout title="Update Your Information" error={null}>
      {!verificationStep ? (
        <UpdateAuthContact
          username={username}
          onSuccess={handleContactInfoSuccess}
          onSkip={handleSkip}
        />
      ) : (
        <VerifyAuthContact
          altUsername={altUsername!}
          onSuccess={handleVerificationSuccess}
        />
      )}
    </RegisterLayout>
  )
}

export default UpdateAuthInfoContainer