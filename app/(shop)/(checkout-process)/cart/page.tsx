'use client'

import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { AxiosError } from "axios"
import CheckoutLayout from "../CheckoutLayout"
import cartStore from "@/src/stores/cart-store"
import { useCart, useDeleteCartItem, useUpdateCart } from "@/src/hooks/cart-hooks"
import { CartItemShape } from "@/src/types/store-types"
import Alert from "@/src/components/utils/alert/Alert"
import { getErrorMessage } from "@/src/components/utils/getErrorMessage"
import EmptyCart from "@/src/components/utils/empty-cart/EmptyCart"
import Spinner from "@/src/components/utils/spinner/Spinner"
import { ErrorResponse } from "@/src/types/types"
import CartItemsList from "@/src/components/checkout/cart/CartItemsList"

const Cart = () => {
  const router = useRouter()
  const [noStockAlert, setNoStockAlert] = useState(false)

  const { cartId } = cartStore()
  const { isPending, error, data } = useCart()
  const { handleQuantityUpdate, mutation } = useUpdateCart()
  const { mutate: deleteCartItem } = useDeleteCartItem()

  const handleIncrement = (item: CartItemShape) => {
    const newQuantity = item.quantity + 1
    handleQuantityUpdate(item.id, newQuantity)
  }

  const handleDecrement = (item: CartItemShape) => {
    if (item.quantity > 1) {
      const newQuantity = item.quantity - 1
      handleQuantityUpdate(item.id, newQuantity)
    }
  }

  const handleCheckout = (cartItems: CartItemShape[]) => {
    const hasOutOfStockItems = cartItems.some(item => item.product_variant.stock_qty === 0)
    if (hasOutOfStockItems) {
      setNoStockAlert(true)
    } else {
      router.push('/checkout')
    }
  }

  useEffect(() => {
    // When cart data changes, check if there are still any out-of-stock items.
    if (data && data?.cart_items?.length > 0) {
      const stillOutOfStock = data?.cart_items.some(item => item.product_variant.stock_qty === 0)
      // Hide the alert if no items are out of stock.
      if (!stillOutOfStock) {
        setNoStockAlert(false)
      }
    }
  }, [data])

  return (
    <div>
      {noStockAlert && <Alert variant="error" message={`
      Some items are out of stock. Please remove them from the cart to proceed with checkout process.`} />}

      {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}

      {!cartId ? <EmptyCart /> :
        isPending ? <Spinner color='#0091CF' size={20} loading={true} /> :
          error ? <Alert variant="error" message={getErrorMessage(error as AxiosError<ErrorResponse>)} /> :
            (!data || data?.cart_items?.length === 0 || Object.keys(data).length === 0) ?
              <EmptyCart /> :
              <CheckoutLayout
                cartItems={data.cart_items}
                totalPrice={data?.total_price}
                shippingCost={data?.shipping_cost}
                grandTotal={data?.grand_total}
                cartWeight={data?.cart_weight}
                showCartItems={false}
                showPriceSummary={true}
                showLogo={false}
                onCheckout={() => handleCheckout(data?.cart_items)}
              >
                <div>
                  <h2>Shopping Cart</h2>
                  <div>
                    <CartItemsList
                      cartItems={data.cart_items}
                      handleIncrement={handleIncrement}
                      handleDecrement={handleDecrement}
                      deleteCartItem={deleteCartItem}
                    />
                  </div>
                </div>
              </CheckoutLayout>
      }
    </div>
  )

}
export default Cart
