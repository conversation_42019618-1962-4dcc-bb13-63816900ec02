import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import APIClient from '../lib/api-client'
import { createOrderShape, OrderShape } from '../types/order-types'
import { CACHE_KEY_ORDER_ITEMS, CACHE_KEY_ORDERS } from '../constants/constants'
import orderStore from '../stores/order-store'
import cartStore from '../stores/cart-store'
import { useState } from 'react'


export const useOrder = (orderId: number) => {

  const apiClient = new APIClient<OrderShape>(`/orders/${orderId}`)

  return useQuery({
    // queryKey: [CACHE_KEY_ORDER_ITEMS, orderId],
    queryKey: [CACHE_KEY_ORDER_ITEMS],
    queryFn: () => apiClient.get(),
    staleTime: 0,
  })

}

export const useGetAllOrders = (page: number, queryString: string = '') => {
  const apiClient = new APIClient<OrderShape>(`/orders/`)
  const queryParams = new URLSearchParams(queryString)

  queryParams.set('page', page.toString())

  return useQuery({
    queryKey: [CACHE_KEY_ORDERS, page, queryString],
    queryFn: () => apiClient.getAll({ params: Object.fromEntries(queryParams) }),
    // staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useCreateOrder = () => {
  // const navigate = useNavigate()
  const { setOrderId } = orderStore()
  const { setCartId } = cartStore()

  const apiClient = new APIClient<OrderShape, createOrderShape>(`/orders/`)

  const createOrder = useMutation<OrderShape, Error, createOrderShape>({
    mutationFn: (data) => apiClient.post(data),
    onSuccess: (data) => {
      console.log(data)
      setOrderId(data.id)
      setCartId(null)
      localStorage.removeItem('cart_store')
      if (data.id) {
        // navigate(`/checkout/order/${data.id}/`)
      }
    },
  })

  return { createOrder }
}

export const useDeleteOrder = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [orderToDelete, setOrderToDelete] = useState<number | null>(null)
  const queryClient = useQueryClient()
  const apiClient = new APIClient(`/orders`)

  const deleteOrder = useMutation({
    mutationFn: (orderId: number) => apiClient.delete(orderId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEY_ORDERS]
      })
    },
  })

  const handleDeleteClick = (orderId: number) => {
    setOrderToDelete(orderId)
    setIsModalOpen(true)
  }

  const confirmDelete = () => {
    if (orderToDelete !== null) {
      deleteOrder.mutate(orderToDelete)
      setIsModalOpen(false)
      setOrderToDelete(null)
    }
  }

  const cancelDelete = () => {
    setIsModalOpen(false)
    setOrderToDelete(null)
  }

  return {
    deleteOrder,
    isModalOpen,
    handleDeleteClick,
    confirmDelete,
    cancelDelete
  }
}