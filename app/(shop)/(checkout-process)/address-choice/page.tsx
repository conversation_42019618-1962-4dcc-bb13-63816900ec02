'use client'

import Link  from "next/link"
import { useEffect, useState } from "react"
import { useRouter } from 'next/navigation'
import CheckoutLayout from "../CheckoutLayout"
import authStore from "@/src/stores/auth-store"
import { useCustomerDetails } from "@/src/hooks/customer-hooks"
import cartStore from "@/src/stores/cart-store"
import { useCart, useDeleteCartItem } from "@/src/hooks/cart-hooks"
import { AddressFormInputs } from "@/src/components/account/addresses/ManageAddresses"
import EmptyCart from "@/src/components/utils/empty-cart/EmptyCart"
import Spinner from "@/src/components/utils/spinner/Spinner"
import Alert from "@/src/components/utils/alert/Alert"

const AddressStage = () => {
  const router = useRouter()

  const { isLoggedIn } = authStore()
  const { data: customer } = useCustomerDetails(isLoggedIn)
  const { cartId, setSelectedAddress, selectedAddress } = cartStore()
  const { isPending, error, data, } = useCart()
  const { mutate: deleteCartItem } = useDeleteCartItem()

  const [addressesReady, setAddressesReady] = useState(false)

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/user/login')
    }
  }, [isLoggedIn, router])

  useEffect(() => {
    if (customer?.address && customer.address.length > 0) {
      if (!selectedAddress || Object.keys(selectedAddress).length === 0) {
        setSelectedAddress(customer.address[0])
      }
      setAddressesReady(true)
    }
  }, [customer, selectedAddress, setSelectedAddress])

  // Handle out-of-stock items by removing them
  useEffect(() => {
    if (data && data?.cart_items?.length > 0) {
      const outOfStockItems = data.cart_items.filter(item => item.product_variant.stock_qty === 0)

      if (outOfStockItems.length > 0) {
        outOfStockItems.forEach(item => {
          deleteCartItem(item.id) // Remove each out-of-stock item from the cart
        })
      }
    }
  }, [data, deleteCartItem])

  const handleAddressChange = (address: AddressFormInputs) => {
    setSelectedAddress(address)
  }

  return (
    <>
      {!cartId ? (
        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />
      ) : (
        <>
          {isPending ? (
            <Spinner color='#0091CF' size={20} loading />
          ) : (
            <>
              {error ? (
                <Alert variant="error" message={error.message} />
              ) : (
                <>
                  {(customer?.address?.length === 0 || customer?.phone_number === '') ? (
                    <>
                      <div>
                        <Alert variant="warning" textSize='20' message="
                        You haven't added a shipping address yet. 
                        Please add one along with a phone number to continue with checkout. Thank you! 😊" />
                        <section className='btn_container'>
                          <button className='empty_btn' onClick={() => router.push('/customer')}>Update Profile</button>
                        </section>
                      </div>
                    </>
                  ) : (
                    <>
                      {!data || data.cart_items.length === 0 ? (
                        <div>
                          <p>Your cart is empty. Add some products to the cart to checkout!</p>
                          <Link href='/'>Go Shopping </Link>
                        </div>
                      ) : (
                        <CheckoutLayout
                          cartItems={data.cart_items}
                          totalPrice={data?.total_price}
                          shippingCost={data?.shipping_cost}
                          grandTotal={data?.grand_total}
                          cartWeight={data?.cart_weight}
                          showCartItems={true}
                          showPriceSummary={true}
                          showLogo={true}
                        >
                          <div>
                            <h3>Address Choices</h3>
                            <div>
                              <h3>Contact Details: </h3>
                              <p>Deliver to: {customer?.first_name} {customer?.last_name}</p>
                              <p>Phone: {customer?.phone_number}</p>
                              <p>Email to: {customer?.email}</p>
                            </div>
                            <hr />
                            {/* Render the addresses only when addresses are ready */}
                            {addressesReady && (
                              <div>
                                <h3>Choose a shipping address: </h3>
                                {customer?.address?.map((address) => (
                                  <address key={address.id}>
                                    <input
                                      type="radio"
                                      id={`address-${address.id}`}
                                      name="address"
                                      value={address.id}
                                      checked={selectedAddress?.id === address.id}
                                      onChange={() => handleAddressChange(address)}
                                    />
                                    <label htmlFor={`address-${address.id}`}>
                                      {address.full_name}, {address.street_name}, {address.city_or_village}
                                    </label>
                                  </address>
                                ))}
                                <button onClick={() => router.push('/checkout/place-order/')}>Use this address</button>
                              </div>
                            )}
                          </div>
                        </CheckoutLayout>
                      )}
                    </>
                  )}
                </>
              )}
            </>
          )}
        </>
      )}
    </>
  )
}

export default AddressStage
