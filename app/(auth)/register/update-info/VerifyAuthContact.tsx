import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { verificationCodeSchema } from '@/src/schemas/schemas'
import { useSendVerifyCode } from '@/src/hooks/auth-hooks'
import Logo from '@/src/components/utils/logo/Logo'
import Alert from '@/src/components/utils/alert/Alert'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import { ErrorResponse } from '@/src/types/types'
import styles from './UpdateAuthInfo.module.scss'

export type VerifyContactShape = z.infer<typeof verificationCodeSchema>

interface Props {
  altUsername: string
  onSuccess: () => void
}

export const VerifyAuthContact = ({ altUsername, onSuccess }: Props) => {
  const { register, handleSubmit, formState: { errors } } = useForm<VerifyContactShape>({
    resolver: zodResolver(verificationCodeSchema),
    // defaultValues: {
    //   altUsername
    // }
  })

  const { mutation } = useSendVerifyCode()

  const onSubmit: SubmitHandler<VerifyContactShape> = async (data) => {
    const verificationData = {
      code: data.code,
      // [data.altUsername?.startsWith('+') ? 'phone_number' : 'email']: data.altUsername,
    }

    mutation.mutate(verificationData, {
      onSuccess
    })
  }

  console.log(altUsername)

  return (
    <div className={styles.register_container}>
      <div className={styles.form_container}>
        <div className='logo_header'>
          <Logo />
        </div>
        {/* <h2 className='title'>Verify Contact Information</h2> */}

        {mutation.error && (
          <Alert
            variant="error"
            message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)}
            textAlign='center'
          />
        )}

        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <Alert
            variant="success"
            message={`A verification code has been sent to ${altUsername}. 
            Please check your ${altUsername?.startsWith('+') ? 'phone' : 'email'} and enter the code below.`}
            textAlign='center'
            highlightWords={[altUsername]}
          />

          <div className='form_group'>
            <label className='form_label'>Enter the verification code:</label>
            {/* <input
              className='form_input'
              type='number'
              {...register("code")}
              disabled={mutation.isPending}
            /> */}
            <input
              className='form_input'
              type='text'
              inputMode='numeric'
              id='verification_code'
              disabled={mutation.isPending}
              onInput={(e) => {
                e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '')
              }}
              {...register("code")}
            // style={{ appearance: 'textfield', MozAppearance: 'textfield' }}
            />
            {errors.code && (
              <p className='form_error'>{errors.code.message}</p>
            )}
          </div>

          <button type="submit" disabled={mutation.isPending}>
            {mutation.isPending ? (
              // <img src={loading_svg} alt="Loading..." className='loading_svg' />
              'Verifying...'
            ) : 'Submit'}
          </button>
        </form>
      </div>
    </div>
  )
}