@use '../../../src/scss/variables' as *;
@use '../../../src/scss/mixins' as *;

.login_container {
  background-image: url(../../../public/images/pc_hardware.jpg);
  background-color: #70829e;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100vh;
  position: relative;
  overflow: hidden; // Add this to prevent the blur from extending outside

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: inherit;
    background-size: cover;
    background-position: center;
    filter: blur(10px);
    z-index: 0; // Change this to 0
  }

  // Add this new rule
  >* {
    position: relative;
    z-index: 1;
  }
}

.form_container {
  width: 400px;
  box-shadow: #0000000d 0px 0px 0px 1px, #d1d5db 0px 0px 0px 1px inset;
  padding: $padding-5;
  background-color: #ffffff;
  border-radius: $border-radius-1;
}

.login_nav {
  h3 {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    margin: 10px 0;
  }

  div {
    @include flexbox(center, center, column);
    row-gap: 8px;

    a {
      font-size: 18px;
      color: $primary-blue;
      text-decoration: underline;
      transition: all .5s ease;

      &:hover {
        color: $primary-lighter-text-color;
      }
    }
  }

}

// @mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {
//   display: flex;
//   justify-content: $justify;
//   align-items: $align;
//   flex-direction: $direction;
//   flex-wrap: $wrap;
// }


.form {
  div {
    @include flexbox(flex-start, stretch, column);
    margin: 10px 0;
    row-gap: 4px;
    
    .password__reset {
      @include flexbox(space-between, center, row);
      width: 100%;
      margin-bottom: 4px;
    }

    label {
      font-weight: bold;
      color: $primary-dark-blue;
    }

    input {
      border: .1px solid $primary-dark-text-color;
      border-radius: 3px;
      padding: 5px 5px;
      font-size: 16.5px;
      width: 100%;

      &:focus {
        outline: 2px solid $lighten-blue;
        border: none;
      }
    }

    p {
      color: $error-red;
      text-align: center;
    }
  }

  .login_btn {
    // margin: 1.5rem auto 1rem auto;
    // @include btn(#fff, $primary-blue);
    // width: 100%;
    // padding: 8px 0;
    // font-size: 18px;

    margin: 1.5rem auto 1rem auto;
    @include btn(#fff, $primary-blue);
    width: 100%;
    padding: 8px 0;
    transition: all 0.3s ease-in;
    font-size: 18px;


    &:hover {
      background-color: darken($primary-blue, 10%);
    }
  }
}

.password__reset {
  flex-direction: row !important;
  margin-bottom: 0 !important;
  justify-content: space-between;

  a {
    color: $primary-blue !important;
    transition: all .3s ease;

    &:hover {
      color: $lighten-blue !important;
    }
  }
}

.login_or_register {
  text-align: center;

  a {
    color: $primary-blue;

    &:hover {
      text-decoration: underline;
    }
  }
}


@media (width > $tablet) {
  .login_container {
    padding-top: 4rem;
  }

  .form_container {
    padding: 25px;
  }
}