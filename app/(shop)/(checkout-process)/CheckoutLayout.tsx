'use client'

import { ReactNode } from 'react'
import Logo from '@/src/components/utils/logo/Logo'
import CartItemsList from '@/src/components/checkout/cart/CartItemsList'
import PriceSummary from '@/src/components/checkout/price-summary/PriceSummary'
import { CartItemShape } from '@/src/types/store-types'
import styles from './CheckoutLayout.module.scss'
import { useRouter } from 'next/navigation'

interface CheckoutLayoutProps {
  children: ReactNode
  cartItems?: CartItemShape[]
  totalPrice?: number
  shippingCost?: number
  grandTotal?: number
  cartWeight?: number
  showCartItems?: boolean
  showPriceSummary?: boolean
  showLogo?: boolean
  className?: string
  onCheckout?: () => void
}

const CheckoutLayout = ({
  children,
  cartItems = [],
  totalPrice = 0,
  shippingCost = 0,
  grandTotal = 0,
  cartWeight = 0,
  showCartItems = true,
  showPriceSummary = true,
  showLogo = true,
  className = '',
  onCheckout
}: CheckoutLayoutProps) => {
  const router = useRouter()

  return (
    <div className={`${styles.checkoutLayout} ${className}`}>
      {showLogo && (
        <div className={styles.logoHeader}>
          <Logo />
        </div>
      )}

      <div className={styles.container}>
        <div className={styles.mainContent}>
          {children}
        </div>

        {showCartItems && cartItems.length > 0 && (
          <div className={styles.sidebar}>
            <div className={styles.cartSection}>
              <h3>Cart Items</h3>
              <CartItemsList cartItems={cartItems} />
            </div>

            {showPriceSummary && (
              <div className={styles.priceSection}>
                <PriceSummary
                  totalPrice={totalPrice}
                  shippingCost={shippingCost}
                  grandTotal={grandTotal}
                  cart_weight={cartWeight}
                  onCheckout={onCheckout}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default CheckoutLayout
