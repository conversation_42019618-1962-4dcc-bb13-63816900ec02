'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { useEffect, useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { useRouter } from 'next/navigation'
import { z } from 'zod'
import authStore from '@/src/stores/auth-store'
import { useCustomerDetails, useUpdateCustomer } from '@/src/hooks/customer-hooks'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import Alert from '@/src/components/utils/alert/Alert'
import Spinner from '@/src/components/utils/spinner/Spinner'
import { ErrorResponse } from '@/src/types/types'
import ManageAddresses from '@/src/components/account/addresses/ManageAddresses'
import Modal from '@/src/components/utils/modal/Modal'
import { upDateProfileSchema } from '@/src/schemas/schemas'
import DateOfBirthModal from '@/src/components/account/change-customer-data/DateOfBirthModal'
import styles from './Profile.module.scss'


export interface UpdateProfileFormInputs extends z.infer<typeof upDateProfileSchema> { }

const Profile = () => {
  const router = useRouter()
  const { register, handleSubmit, reset, formState: { errors } } = useForm<UpdateProfileFormInputs>({
    resolver: zodResolver(upDateProfileSchema),
  })

  const { isLoggedIn } = authStore()
  const { isPending, error, data: customerData } = useCustomerDetails(isLoggedIn)
  const { mutation: customerUpdater } = useUpdateCustomer()

  const [isConfirmModalOpen, setConfirmModalOpen] = useState(false)
  const [pendingData, setPendingData] = useState<{ value: string } | null>(null)
  const [isEditMode, setIsEditMode] = useState(false)
  const [modalConfig, setModalConfig] = useState<{
    isOpen: boolean
    initialData: string
    isEditing: boolean
  }>({
    isOpen: false,
    initialData: '',
    isEditing: false,
  })

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login')
    }
  }, [isLoggedIn, router])

  useEffect(() => {
    if (customerData) {
      reset({
        first_name: customerData.first_name,
        last_name: customerData.last_name,
      })
    }
  }, [customerData, reset])

  const onSubmit: SubmitHandler<UpdateProfileFormInputs> = async (userDetails) => {
    customerUpdater.mutate(userDetails, {
      onSuccess: () => {
        setIsEditMode(false)
      },
    })
  }

  const handleInputSubmit = (data: { value: string }) => {
    setPendingData(data)
    setConfirmModalOpen(true)
  }

  const openModal = () => {
    setModalConfig({
      isOpen: true,
      initialData: customerData?.birth_date || '',
      isEditing: Boolean(customerData?.birth_date),
    })
  }

  const confirmUpdate = () => {
    if (!pendingData) return

    customerUpdater.mutate({ birth_date: pendingData.value }, {
      onSuccess: () => {
        console.log('Date of birth updated successfully')
        setModalConfig((prev) => ({ ...prev, isOpen: false }))
      }
    })
    setConfirmModalOpen(false)
    setPendingData(null)
  }

  return (
    <div className={styles.profile}>
      {isPending ? <Spinner color='#0091CF' size={20} loading={true} /> :
        error ? <Alert
          variant="error"
          message={getErrorMessage(error as AxiosError<ErrorResponse>)}
          textAlign='center'
        /> :
          <>
            <h2>Manage your Profile</h2>
            <div className={styles.user_details}>
              {!isEditMode ? (
                <section className={styles.profile__details}>
                  <div>
                    <h5>First Name:</h5>
                    <p>{customerData?.first_name}</p>
                  </div>
                  <div>
                    <h5>Last Name:</h5>
                    <p>{customerData?.last_name}</p>
                  </div>
                  <button className={styles.edit_btn} type="button" onClick={() => setIsEditMode(true)}>
                    Edit
                  </button>
                </section>
              ) : (
                <form onSubmit={handleSubmit(onSubmit)} className={styles.profile__editable}>
                  <div>
                    <label htmlFor='first_name'>First Name:</label>
                    <input id='first_name' type="text" {...register('first_name')} />
                    {errors.first_name && <p>{errors.first_name.message}</p>}
                  </div>
                  <div>
                    <label htmlFor='last_name'>Last Name:</label>
                    <input id='last_name' type="text" {...register('last_name')} />
                    {errors.last_name && <p>{errors.last_name.message}</p>}
                  </div>
                  <div>
                    <button className={styles.edit_btn} type="submit">Save</button>
                    <button className={styles.edit_btn__empty} onClick={() => setIsEditMode(false)}>Cancel</button>
                  </div>
                </form>
              )}

              <section className={styles.reset_details}>
                <section className={styles.reset_details__section}>
                  <div>
                    <h5>Email:</h5>
                    <p>{customerData?.email}</p>
                  </div>
                  <button className={styles.edit_btn} onClick={() => router.push('/user/change-email')}>Edit</button>
                </section>
                <section className={styles.reset_details__section}>
                  <div>
                    <h5>Password</h5>
                    <p>********</p>
                  </div>
                  <button className={styles.edit_btn} onClick={() => router.push('/user/new-password')}>Change</button>
                </section>

                {/* Phone Number & Date of Birth Section */}
                <section className={styles.reset_details__section}>
                  <div>
                    <h5>Phone number:</h5>
                    {customerData?.phone_number ? <p>{customerData?.phone_number}</p> :
                      <p className={styles.no_data}>No phone number added</p>}
                    <button className={styles.edit_btn} onClick={() => router.push('/user/change-phone-number')}>
                      {customerData?.phone_number ? 'Edit' : 'Add'}
                    </button>
                  </div>
                  <div>
                    <h5>Date of Birth:</h5>
                    {customerData?.birth_date ? (
                      <p>{customerData?.birth_date}</p>
                    ) : (
                      <p className={styles.no_data}>No date of birth added</p>
                    )}
                    <button
                      className={styles.edit_btn}
                      onClick={() => openModal()}
                    >
                      {customerData?.birth_date ? 'Edit' : 'Add'}
                    </button>
                  </div>
                </section>
              </section>
            </div>

            {/* Manage Addresses */}
            <ManageAddresses addresses={customerData?.address || []} />
          </>}

      {/* Flexible Input Modal */}
      {modalConfig.isOpen && (
        <DateOfBirthModal
          isPending={customerUpdater.isPending}
          error={customerUpdater.error}
          onSubmit={handleInputSubmit}
          onClose={() => setModalConfig(prev => ({ ...prev, isOpen: false }))}
          initialData={modalConfig.initialData}
          isEditing={modalConfig.isEditing}
          isOpen={modalConfig.isOpen}
          title={`${modalConfig.isEditing ? 'Edit' : 'Add'} Date of Birth`}
        />
      )}

      {isConfirmModalOpen && (
        <Modal
          title="Confirm Update"
          message="Are you sure you want to update your birthday? Birthday can be updated only twice."
          show={isConfirmModalOpen}
          onClose={() => setConfirmModalOpen(false)}
          onConfirm={confirmUpdate}
        />
      )}

    </div>
  )
}

export default Profile
