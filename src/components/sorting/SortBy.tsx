import React from 'react'
import { useSearchParams } from 'react-router-dom'
import styles from './SortBy.module.scss'

// Define sorting options
const sortOptions = [
  { value: 'title_asc', label: 'Title (A-Z)' },
  { value: 'title_desc', label: 'Title (Z-A)' },
  { value: 'price_asc', label: 'Price (Low to High)' },
  { value: 'price_desc', label: 'Price (High to Low)' },
]

const Sorting = () => {
  // Use the useSearchParams hook to get and set URL search parameters
  const [sortingParams, setSearchParams] = useSearchParams()

  // Handle changes in the sorting select element
  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newParams = new URLSearchParams(sortingParams)

    if (e.target.value === '') {
      // Remove the 'sort_by' parameter if the default option is selected
      newParams.delete('sort_by')
    } else {
      // Set the 'sort_by' parameter with the selected value
      newParams.set('sort_by', e.target.value)
    }

    setSearchParams(newParams)
  }

  return (
    <div className={styles.sorting}>
      <select
        id="sort-select"
        className={styles.select}
        value={sortingParams.get('sort_by') || ''}
        onChange={handleSortChange}
      >
        <option value="">Sort by: Default</option>
        {sortOptions.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )
}

export default Sorting
