import React from "react"
import { RiQuestionFill } from "react-icons/ri"
import Tooltip from '../../utils/tooltip/Tooltip'
import styles from './PriceSummary.module.scss'

interface PriceSummaryProps {
  totalPrice: number
  shippingCost: number
  grandTotal: number
  cart_weight: number
  onCheckout?: () => void
}

const PriceSummary: React.FC<PriceSummaryProps> = ({ totalPrice, shippingCost, grandTotal, cart_weight, onCheckout }) => {
  return (
    <div className={styles.cart__checkout}>
      <div>
        <p>Item's cost: </p> <p>${totalPrice.toFixed(2)}</p>
      </div>
      <div>
        <p>Shipping cost:
          <Tooltip
            content={`Cost for packaging & weight of cart items (${cart_weight}g)`}
            position="top"
          >
            <i><RiQuestionFill /></i>
          </Tooltip>
        </p>
        <p>${shippingCost.toFixed(2)}</p>
      </div>
      <div>
        <p>Total: </p> <p>${grandTotal.toFixed(2)}</p>
      </div>
      {onCheckout && <button className="btn" onClick={onCheckout}>Proceed to checkout</button>}
    </div>
  )
}

export default PriceSummary
