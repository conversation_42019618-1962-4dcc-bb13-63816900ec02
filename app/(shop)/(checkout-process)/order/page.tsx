'use client'

import { useParams } from 'next/navigation'
import { DateTime } from 'luxon'
import payment_cards from '../../../../public/images/credit_cards.png'
import { loadStripe } from "@stripe/stripe-js"
import { Elements } from '@stripe/react-stripe-js'
import { AxiosError } from 'axios'
import noImagePlaceholder from '../../../assets/no-image-placeholder.png'
import CheckoutLayout from "../CheckoutLayout"
import { useOrder } from '@/src/hooks/order-hooks'
import EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'
import Spinner from '@/src/components/utils/spinner/Spinner'
import Alert from '@/src/components/utils/alert/Alert'
import Link from 'next/link'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import LimitTitleLength from '@/src/components/utils/TextLimit'
import CheckoutForm from '@/src/components/checkout/stripe-checkout/CheckoutForm'
import PayPalCheckout from '@/src/components/utils/paypal/PayPalCheckout'
import { OrderedItemShape } from '@/src/types/order-types'
import { useClientSecret } from '@/src/hooks/checkout-hooks'
import { ErrorResponse } from '@/src/types/types'


const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY!)

const Order = () => {
  const params = useParams()
  const id = params.id
  const orderId = Number(id)
  const { isPending, error, data: order } = useOrder(orderId)
  const { data: clientSecret } = useClientSecret(orderId)

  console.log(id)

  console.log('placed order:', order)

  console.log('clientSecret:', clientSecret)


  return (
    <div>
      {!id ? <EmptyCart message='It seems that you have not placed any orders yet. Please add some products to the cart to checkout!' /> :
        isPending ? <Spinner color='#0091CF' size={20} loading={true} bgOpacity={0} /> :
          error ? <Alert variant="error" message={getErrorMessage(error as AxiosError<ErrorResponse>)} /> :
            !order ? ( // If there  is no order with a given ID, Django with throw a 404 error
              <div>
                <p>Your cart is empty. Add some products to the cart to checkout!</p>
                <Link href='/'>Go Shopping</Link>
              </div>
            ) : (
              <CheckoutLayout
                showCartItems={false}
                showPriceSummary={false}
                showLogo={true}
              >
                <div>
                  <h3>Order Summary</h3>
                  <div>
                    <section>
                      <div>
                        <h3>Placed at: </h3>
                        <span>{DateTime.fromISO(order.placed_at).toFormat('EEEE, MMMM d, yyyy, h:mm a')}</span>
                      </div>
                      <div>
                        <div>
                          <h3>Contact Details: </h3>
                          <p>Deliver to: {order.customer?.first_name} {order.customer?.last_name}</p>
                          <p>Phone: {order.customer?.phone_number}</p>
                          <p>Email to: {order.customer?.email}</p>
                        </div>
                        <div>
                          <h3>Shipping address: </h3>
                          <address>
                            {order.selected_address.full_name},<br />
                            {order.selected_address.street_name},<br />
                            {order.selected_address.postal_code},<br />
                            {order.selected_address.city_or_village}<br />
                          </address>
                        </div >
                      </div>
                      <hr />
                      <div>
                        <ul>
                          {order?.ordered_items?.map((item: OrderedItemShape) => (
                            <li key={item.id}>
                              <div>
                                <img
                                  src={item.product_variant?.product_image?.[0]?.image
                                    ? `${import.meta.env.VITE_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`
                                    : noImagePlaceholder}
                                  alt={item.product.title || "Product image"}
                                />
                              </div>
                              <div>
                                <span>
                                  <Link href={`/products/${item.product.slug}/`}>
                                    <LimitTitleLength title={item.product.title} maxLength={60} />
                                  </Link>
                                </span>
                                <span>(${item.product_variant.price} x {item.quantity})</span>
                                <span>Variant: {item.product_variant?.price_label?.attribute_value}</span>
                                {Object.entries(item.extra_data).map(([key, value], index) => (
                                  <div key={index}>
                                    <p>{key} :</p><p>{value}</p>
                                  </div>
                                ))}
                              </div>
                              <div>
                                <p>Qty : {item.quantity}</p>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </div>
                      <hr />
                      <div>
                        <h3>Payment Method:</h3>
                        <div>
                          {order?.payment_method.id == 2 &&
                            <img src={payment_cards} alt="payment cards" />}
                          <p>{order?.payment_method.name}</p>
                        </div>
                      </div>
                    </section >
                    <section>
                      <div>
                        <h3>Delivery Status:</h3>
                        <Alert
                          variant={
                            order?.delivery_status === 'Pending' ? 'warning' :
                              order?.delivery_status === 'Processing' ? 'info' :
                                'success'
                          }
                          message={order?.delivery_status}
                        />
                      </div>
                      <div>
                        <h3>Payment Status:</h3>
                        <Alert variant={order?.payment_status === 'Pending' ? 'warning' : 'success'} message={order?.payment_status} />
                      </div>
                      <div>
                        <div>
                          <p>Total :</p>
                          <p>${order?.total}</p>
                        </div>
                      </div>
                      <div>
                        {order?.payment_status === "Pending" && order?.payment_method?.slug === 'paypal' ? (
                          <div>
                            <p>Pay with PayPal:</p>
                            <PayPalCheckout
                              orderId={order.id}
                              amount={order.total}
                            />
                          </div>
                        ) :
                          order?.payment_status === "Pending" && order?.payment_method?.slug === 'stripe' ? (
                            <div>
                              {clientSecret && <Elements
                                options={{
                                  clientSecret: clientSecret.client_secret,
                                }}
                                stripe={stripePromise}>
                                <CheckoutForm
                                  amount={order?.total}
                                  orderId={orderId}
                                />
                              </Elements>}
                            </div>
                          ) : (
                            <div>
                              {order?.payment_status === "Paid" && <p>Thank you for your purchase.</p>}
                              <Link href='/my-orders'>My orders</Link>
                            </div>
                          )}
                      </div>
                    </section>
                  </div>
                </div>
              </CheckoutLayout>
            )}
    </div >
  )
}

export default Order