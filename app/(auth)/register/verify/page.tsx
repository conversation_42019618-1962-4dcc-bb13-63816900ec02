'use client'

import { useForm, SubmitHandler } from 'react-hook-form'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { verificationCodeSchema } from '@/src/schemas/schemas'
import { ErrorResponse } from '@/src/types/types'
import Underlay from '@/src/components/utils/underlay/Underlay'
import authStore from '@/src/stores/auth-store'
import { useSendVerifyRegCredentials } from '@/src/hooks/auth-hooks'
import RegisterLayout from '../RegisterLayout'
import Alert from '@/src/components/utils/alert/Alert'

type VerifyCodeFormInputs = z.infer<typeof verificationCodeSchema>

export interface VerificationCredentials {
  code: number
  email?: string
  phone_number?: string
}

const VerifyRegCredentials = () => {
  const router = useRouter()
  const { username,
    regInitiated,
    setRegInitiated,
    setUsername,
    verificationCodeSubmitted,
    setVerificationCodeSubmitted,
  } = authStore()
  const { mutation } = useSendVerifyRegCredentials()


  const { register, handleSubmit, formState: { errors }, watch } = useForm<VerifyCodeFormInputs>({
    resolver: zodResolver(verificationCodeSchema)
  })

  const allValues = watch()

  console.log(typeof (allValues.code))

  console.log('Username in VerifyRegCredentials:', username)

  useEffect(() => {
    if (verificationCodeSubmitted) {
      router.push('/register/set-password')
    }
  }, [verificationCodeSubmitted, router])


  useEffect(() => {
    if (!regInitiated) {
      router.push('/register/initiate/')
    }

    if (mutation.isSuccess) {
      router.push('/register/set-password/')
    }

    // If a user did not continue the registration process, redirect them to the registration page after 15 minutes
    // setTimeout(() => {
    //   setUsername(null)
    //   setRegInitiated(false)
    //   router.push('/user/register')
    // }, 900000) // 15 minutes
  }, [setRegInitiated, mutation.isSuccess, router, setUsername, regInitiated])


  const onSubmit: SubmitHandler<VerifyCodeFormInputs> = (data) => {

    const submissionData: VerificationCredentials = { code: data.code }

    if (username) {
      if (username.startsWith('+')) {
        submissionData.phone_number = username
      } else {
        submissionData.email = username
      }
    }
    mutation.mutate(submissionData, {
      onSuccess: () => {
        setVerificationCodeSubmitted(true)
      }
    })  // Submit the mutation
  }


  return (
    <Underlay isOpen>
      {regInitiated && (
        <RegisterLayout title="Verify your account" error={mutation.error as AxiosError<ErrorResponse>}>
          <Alert
            variant="success"
            message={`A Verification Code has been sent to ${username}. 
            Please check your ${username?.startsWith('+') ? 'phone' : 'email'} and enter the code below.`}
            textAlign='center'
            highlightWords={[`${username}`, "phone", "email"]}
          />
          <form onSubmit={handleSubmit(onSubmit)} className='form' noValidate={true}>
            <div className='form_group'>
              <label className='form_label' htmlFor="verification_code">Enter verification code:</label>
              <section className='password__container'>
                {/* <input
                  className='form_input'
                  type='text'
                  inputMode='numeric'
                  id='verification_code'
                  disabled={mutation.isPending}
                  {...register("code")}
                /> */}
                <input
                  className='form_input'
                  type='text'
                  inputMode='numeric'
                  id='verification_code'
                  disabled={mutation.isPending}
                  onInput={(e) => {
                    e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '')
                  }}
                  {...register("code")}
                // style={{ appearance: 'textfield', MozAppearance: 'textfield' }}
                />
              </section>
              {errors.code && <p className='form_error'>{errors.code.message} &#128543;</p>}
            </div>
            <section className='btn_container'>
              <button type="submit" className='empty_btn' disabled={mutation.isPending}>
                {mutation.isPending ? (
                  // <img src={loading_blue} alt="Loading..." className='loading_svg' />
                  'Verifying...'

                ) : (
                  'Submit'
                )}
              </button>
            </section>
          </form>
        </RegisterLayout>
      )}
    </Underlay>
  )
}

export default VerifyRegCredentials
