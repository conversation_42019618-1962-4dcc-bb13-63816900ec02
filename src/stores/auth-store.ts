// Pure cookie-based authentication store for e-commerce
// No persistence needed - authentication state is managed server-side via HTTP-only cookies

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { User, LoginCredentials, RegisterData } from '../types';

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setUser: (user: User | null) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  logout: () => void;
  reset: () => void;

  // Computed getters
  isGuest: () => boolean;
  fullName: () => string;
  initials: () => string;
}

const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true, // Start with loading true to prevent premature redirects
  error: null,
};

export const useAuthStore = create<AuthState>()(
  devtools(
    (set, get) => ({
      // Initial state
      ...initialState,

      // Actions
      setUser: (user) => {
        set({ 
          user,
          isAuthenticated: !!user,
          error: null 
        });
      },

      setAuthenticated: (authenticated) => {
        set({ 
          isAuthenticated: authenticated,
          user: authenticated ? get().user : null 
        });
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      setError: (error) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          error: null,
          isLoading: false,
        });
      },

      updateProfile: async (profileData: Partial<User>) => {
        const { user } = get();
        if (!user) throw new Error('No user to update');

        set({ isLoading: true, error: null });

        try {
          // TODO: Replace with actual API call
          // const updatedUser = await AuthService.updateProfile(profileData);

          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));

          const updatedUser = { ...user, ...profileData };

          set({
            user: updatedUser,
            isLoading: false,
            error: null
          });

          return updatedUser;
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Failed to update profile'
          });
          throw error;
        }
      },

      reset: () => {
        set(initialState);
      },

      // Computed getters
      isGuest: () => !get().isAuthenticated,

      fullName: () => {
        const { user } = get();
        if (!user) return '';
        return `${user.first_name} ${user.last_name}`.trim();
      },

      initials: () => {
        const { user } = get();
        if (!user) return '';
        const firstInitial = user.first_name?.charAt(0)?.toUpperCase() || '';
        const lastInitial = user.last_name?.charAt(0)?.toUpperCase() || '';
        return `${firstInitial}${lastInitial}`;
      },
    }),
    {
      name: 'AuthStore',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// Selector hooks for better performance
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);
export const useAuthError = () => useAuthStore((state) => state.error);
export const useIsGuest = () => useAuthStore((state) => state.isGuest());
export const useFullName = () => useAuthStore((state) => state.fullName());
export const useInitials = () => useAuthStore((state) => state.initials());

// Auth actions - memoized to prevent unnecessary re-renders
export const useAuthActions = () => useAuthStore((state) => ({
  setUser: state.setUser,
  setAuthenticated: state.setAuthenticated,
  setLoading: state.setLoading,
  setError: state.setError,
  clearError: state.clearError,
  logout: state.logout,
  updateProfile: state.updateProfile,
  reset: state.reset,
}), (a, b) =>
  a.setUser === b.setUser &&
  a.setAuthenticated === b.setAuthenticated &&
  a.setLoading === b.setLoading &&
  a.setError === b.setError &&
  a.clearError === b.clearError &&
  a.logout === b.logout &&
  a.updateProfile === b.updateProfile &&
  a.reset === b.reset
);

// Helper hooks for common auth patterns
export const useAuthState = () => {
  const user = useUser();
  const isAuthenticated = useIsAuthenticated();
  const isLoading = useAuthLoading();
  const error = useAuthError();
  const isGuest = useIsGuest();

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    isGuest,
    fullName: useFullName(),
    initials: useInitials(),
  };
};

export const useRequireAuth = () => {
  const isAuthenticated = useIsAuthenticated();
  const isLoading = useAuthLoading();
  
  return {
    isAuthenticated,
    isLoading,
    requiresAuth: !isAuthenticated && !isLoading,
  };
};
