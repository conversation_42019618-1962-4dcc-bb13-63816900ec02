'use client'

import { useForm, SubmitHandler } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import { loginSchema } from '@/src/schemas/schemas'
import { useLogin } from '@/src/hooks/auth-hooks'
import { useTogglePasswordVisibility } from '@/src/hooks/other-hooks'
import Logo from '@/src/components/utils/logo/Logo'
import Alert from '@/src/components/utils/alert/Alert'
import Link from 'next/link'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import { ErrorResponse } from '@/src/types/types'
import styles from './Login.module.scss'


export type LoginUserShape = z.infer<typeof loginSchema>

const Login = () => {
  const { isVisible, toggleVisibility } = useTogglePasswordVisibility()
  const { mutation } = useLogin()

  const { register, handleSubmit, formState: { errors } } = useForm<LoginUserShape>({
    resolver: zodResolver(loginSchema)
  })

  const onSubmit: SubmitHandler<LoginUserShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: () => {
        // reset()
        // setTimeout(() => {
        //   navigate('/')
        //   // navigate('/checkout/')
        // }, 4000)
      }
    })
  }


  return (
    <div className={styles.login_container}>
      <div className={`${styles.form_container} container`}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.isSuccess ?
          <div>
            <Alert variant="success" message='Login successful.' textAlign='center' />
            <div className={styles.login_nav}>
              <h3>Navigate me to:</h3>
              <div>
                {/* <Link href='/checkout'>Continue Checkout</Link> */}
                <Link href='/account/profile/'>Update Profile</Link>
                {/* <Link href='/cart'>Shopping Cart</Link> */}
              </div>
            </div>
          </div> :
          <div>
            {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
            {/* {mutation.error && <Alert variant="error" message={`Login failed. Please try again later.`} />} */}
            <h2 className='title'>Login</h2>
            <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
              <div>
                <label htmlFor="username">Email or phone number:</label>
                <input placeholder={` Eg: +***********`} type="text" id="username" {...register("username")} />
                {errors.username && <p>{errors.username.message}</p>}
              </div>
              <div>
                <div className={styles.password__reset}>
                  <label htmlFor="password">Password:</label>
                  <Link href='/user/password-reset/'>Forget password?</Link>
                </div>
                <section className='password__container'>
                  <input
                    type={isVisible ? "text" : "password"} id="password" {...register("password")} />
                  <span onClick={toggleVisibility}>
                    <i>{isVisible ? <FaEyeSlash /> : <FaEye />}</i>
                  </span>
                </section>
                {errors.password && <p>{errors.password.message}</p>}
              </div>
              <button className={styles.login_btn} type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? (
                  // <img src={loading} alt="Loading..." className='loading_svg' />
                  'Logging in...'
                ) : (
                  'Login'
                )}
              </button>
              <p className={styles.login_or_register}>Don&apos;t have an account yet? <Link href='/user/register'>Register</Link></p>
            </form>
          </div>
        }
      </div>
    </div>
  )
}

export default Login
