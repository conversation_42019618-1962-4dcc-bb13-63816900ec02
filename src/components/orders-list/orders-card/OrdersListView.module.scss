@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.orders {
  @include flexbox(flex-start, stretch, column);
  gap: 1rem;
}

.orders_list {
  box-shadow: $box-shadow-2;
  padding: 1rem 1rem 1rem 1rem;
  // background-color: #7471f7;
  display: grid;
  grid-template-columns: 100%;
  // border-radius: 10px;

  .order_id {
    @include flexbox(center, center);
    padding: .5rem 0;
    color: $primary-blue;
    font-weight: bold;
    font-size: $font-size-3;
  }


  &__order_items {
    // background-color: #c3fae8;
    padding: 1rem;
    gap: 1rem;
  }

  &__status {
    // display: grid;
    // grid-template-columns: 1fr;
    // gap: 1rem;
    // margin-bottom: 1.5rem;
    @include flexbox(center, stretch, column);

    section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      // align-items: center;
      gap: 1rem;
      padding: 0.5rem;
      // background-color: $secondary-color;
      // border-radius: 4px;
      // @include card-shadow;

      h3 {
        // font-size: 0.9rem;
        font-weight: bold;
        // color: $text-color;
        // margin: 0;
        color: $primary-dark-text-color;
      }

      p {
        // font-size: 0.9rem;
        // color: $primary-color;
        // margin: 0;
        text-align: left;
      }
    }
  }

  .view_order {
    @include flexbox(center, center);
    width: 100%;
    column-gap: 1rem;
    padding: 1rem;
    text-align: center;
    text-decoration: underline $primary-blue;

    a {
      color: $primary-blue;
      transition: all 0.3s ease;


      &:hover {
        color: darken($primary-blue, 10%);
      }
    }

    button {
      @include btn($error-red, #fff);
      padding: .6rem;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        background-color: lighten($error-red, 40%);
      }
    }
  }
}

@media (width > $tablet) {
  .orders_list {
    grid-template-columns: 5% 45% 45% 5%;

    .view_order {
      padding: 0;
      width: 4rem;
      flex-direction: column;
      row-gap: 1rem;
    }
  }
}

.cart__item_list {
  width: 100%;
}

.cart__cart_item {
  padding: 1rem 0;
  margin: 0;
  width: 100%;
  display: grid;
  grid-template-columns: 20% 60% 20%;
  box-shadow: $box-shadow-1;
}

.cart_items_img {
  margin: 0 1rem;
  max-width: 100px;
  @include flexbox(center, center);

  img {
    object-fit: contain;
  }
}

.cart_items_info {
  @include flexbox(flex-start, flex-start, column);
  row-gap: .2rem;

  div {
    @include flexbox(flex-start, center);
    column-gap: .2rem;

    p {
      font-weight: bold;
      font-size: 15px;
      color: $primary-lighter-text-color;
    }
  }

  >a:first-child {
    font-weight: bold;
  }
}