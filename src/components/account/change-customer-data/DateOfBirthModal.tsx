import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import styles from './DateOfBirthModal.module.scss'
import { getErrorMessage } from '../../utils/getErrorMessage'
import { ErrorResponse } from '@/src/types/types'
import Underlay from '../../utils/underlay/Underlay'
import Alert from '../../utils/alert/Alert'
import { validateDateOfBirth } from '@/src/lib/utils'

const inputSchema = z.object({
  value: z.string()
    .min(1, { message: 'Birth date is required' })
    .regex(/^\d{4}-\d{2}-\d{2}$/, { message: 'Date must be in the format YYYY-MM-DD' })
    .refine((val) => validateDateOfBirth(val), { message: 'You must be at least 18 years old' })
})

export type InputFormData = z.infer<typeof inputSchema>

interface Props {
  isPending: boolean
  error: Error | AxiosError | null
  onSubmit: (data: InputFormData) => void
  onClose: () => void
  initialData: string
  isEditing: boolean
  isOpen: boolean
  title: string
}

const DateOfBirthModal = ({
  onSubmit,
  onClose,
  initialData,
  isEditing,
  isOpen,
  title,
  error,
  isPending
}: Props) => {
  const { register, handleSubmit, formState: { errors } } = useForm<InputFormData>({
    resolver: zodResolver(inputSchema),
    defaultValues: { value: initialData },
  })

  const onSubmitForm: SubmitHandler<InputFormData> = (data) => {
    onSubmit(data)
  }

  return (
    <Underlay isOpen={isOpen}>
      <div className={styles.modal_content}>
        <h3>{title}</h3>
        {error && <Alert variant='error' message={getErrorMessage(error as AxiosError<ErrorResponse>)} />}
        <form onSubmit={handleSubmit(onSubmitForm)}>
          <div>
            <label htmlFor='value'>Date of Birth:</label>
            <input
              id='value'
              type='date'
              max={new Date().toISOString().split('T')[0]}
              {...register('value')}
            />
            {errors.value && <p>{errors.value.message}</p>}
          </div>
          <div className={styles.modal_buttons}>
            <button type="submit" className='empty_btn' disabled={isPending}>
              {isPending ? (
                // <img src={loading_blue} alt="Loading..." className='loading_svg' />
                'Updating...'
              ) : (
                isEditing ? 'Update' : 'Save'
              )}
            </button>
            <button type="button" onClick={onClose}>Cancel</button>
          </div>
        </form>
      </div>
    </Underlay>
  )
}

export default DateOfBirthModal
