import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { ProductImageShape } from '../types/product-types'
import APIClient from '../lib/api-client'
import { WISHLIST_ITEMS } from '../constants/constants'


interface WishlistShape {
  id: number
  product: {
    id: number
    title: string
    slug: string
    is_active: boolean
    average_rating: number
    product_variant: [
      {
        id: number
        price: number
        product_image: ProductImageShape[]
      }
    ]
  }
  added_at: string
}

export const useWishlist = (page: number, enabled: boolean = true) => {

  const apiClient = new APIClient<WishlistShape>(`/wishlist/`)

  return useQuery({
    queryKey: [WISHLIST_ITEMS, page],
    queryFn: () => apiClient.getAll({
      params: {
        page: page
      }
    }),
    enabled, // Only run the query if enabled is true (i.e., user is authenticated)
    // keepPreviousData: true,
    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
    // refetchOnMount: true,
    staleTime: 0,

  })
}

export const useToggleWishlist = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/wishlist/toggle_product/`)

  const mutation = useMutation({
    mutationFn: (productId: number) => apiClient.post({
      "product_id": `${productId}`
    }),
    // onMutate: async (itemId) => {
    //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)

    //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)

    //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {
    //     const updatedCartItems = oldData?.cart_items.filter(
    //       (item) => item.id !== itemId
    //     )
    //     return { ...oldData, cart_items: updatedCartItems }
    //   })

    //   return { previousCartItems }
    // },

    onSuccess: () => {
      // queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)
      queryClient.invalidateQueries({
        queryKey: [WISHLIST_ITEMS]
      })
    },

    // onError: (err, itemId, context) => {
    //   if (context?.previousCartItems) {
    //     queryClient.setQueryData(CACHE_KEY_CART_ITEMS, context.previousCartItems)
    //   }
    // },

    // onSettled: async () => {
    //   return await queryClient.invalidateQueries({
    //     queryKey: CACHE_KEY_CART_ITEMS
    //   })
    // },

    // onSettled: () => {
    //   queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)
    //   // queryClient.invalidateQueries({ queryKey: CACHE_KEY_CART_ITEMS });
    // },

  })

  // const handleDeleteCartItem = (itemId) => {
  //   mutate({ itemId })
  // }

  // return { isPending, isError, mutate }

  return mutation

}

export const useDeleteWishlistItem = () => {
  const queryClient = useQueryClient()
  const apiClient = new APIClient('/wishlist')

  const mutation = useMutation({
    mutationFn: (itemId: number) => {
      // This will correctly construct the URL as /wishlist/123/
      return apiClient.delete(itemId)
    },
    onSuccess: () => {
      // Invalidate the wishlist query to refetch the latest data
      queryClient.invalidateQueries({
        queryKey: [WISHLIST_ITEMS]
      })
    },
    onError: (error) => {
      // Optional: Add error handling
      console.error('Failed to delete wishlist item:', error)
    }
  })

  return mutation
}

