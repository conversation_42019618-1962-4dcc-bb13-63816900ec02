@use '../../../../src/scss/variables' as *;
@use '../../../../src/scss/mixins' as *;

.title {
  font-size: $font-size-4;
  font-weight: bold;
  margin: 1rem 0;
}

.addresses {
  width: 100%;

  &__list {
    padding: 1rem;
    border-radius: $border-radius-3;
    margin-bottom: 1rem;

    div {
      margin-bottom: 0.5rem;

      h5 {
        font-weight: bold;
        color: $primary-dark-text-color;
        margin-bottom: 0.25rem;
        font-size: 16px;
      }

      p {
        color: $primary-lighter-text-color;
      }
    }

    &__edit_buttons {
      @include flexbox(space-between, center);

      button:first-child {
        @include btn($primary-blue, #fff);
        padding: .3rem 1rem;
        border: 1px solid $lighten-blue;
        transition: all 0.3s ease;

        &:hover {
          border: 1px solid $primary-dark-blue;
          color: $primary-dark-blue;
        }
      }

      button:last-child {
        @include btn($error-red, #fff);
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background-color: lighten($error-red, 40%);
        }
      }
    }
  }
}

@media (width > $mobile) {

  .addresses {
    width: 100%;
    max-width: 600px;
    @include flexbox(space-between, flex-start);
    flex-wrap: wrap;

    .addresses__list {
      width: 300px;
    }
  }
}