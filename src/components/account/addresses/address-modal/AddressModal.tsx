import { useForm, SubmitHandler } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { addressSchema } from "@/src/schemas/schemas"
import Underlay from "@/src/components/utils/underlay/Underlay"
import styles from './AddressModal.module.scss'


type AddressFormInputs = z.infer<typeof addressSchema>

interface Props {
  onSubmit: (data: AddressFormInputs) => void
  onClose: () => void
  initialData?: AddressFormInputs
  isEditing: boolean
  isOpen: boolean
}

const AddressModal = ({ onSubmit, onClose, initialData, isEditing }: Props) => {

  const { register, handleSubmit, formState: { errors } } = useForm<AddressFormInputs>({
    resolver: zodResolver(addressSchema),
    defaultValues: initialData,
  })

  const onSubmitForm: SubmitHandler<AddressFormInputs> = (data) => {
    onSubmit(data)
  }

  return (
    <Underlay isOpen={true}>
      <div className={styles.modal_content}>
        <h3>{isEditing ? 'Edit Address' : 'Add New Address'}</h3>
        <form onSubmit={handleSubmit(onSubmitForm)}>
          <div>
            <label htmlFor='full_name'>Full Name:</label>
            <input id='full_name' type="text" {...register('full_name')} />
            {errors.full_name && <p>{errors.full_name.message}</p>}
          </div>
          <div>
            <label htmlFor='street_name'>Street Name:</label>
            <input id='street_name' type="text" {...register('street_name')} />
            {errors.street_name && <p>{errors.street_name.message}</p>}
          </div>
          <div>
            <label htmlFor='address_line_1'>Address Line 1: (Optional)</label>
            <input id='address_line_1' type="text" {...register('address_line_1')} />
            {errors.address_line_1 && <p>{errors.address_line_1.message}</p>}
          </div>
          <div>
            <label htmlFor='address_line_2'>Address Line 2: (Optional)</label>
            <input id='address_line_2' type="text" {...register('address_line_2')} />
            {errors.address_line_2 && <p>{errors.address_line_2.message}</p>}
          </div>
          <div>
            <label htmlFor='postal_code'>Postal Code:</label>
            <input id='postal_code' type="text" {...register('postal_code')} />
            {errors.postal_code && <p>{errors.postal_code.message}</p>}
          </div>
          <div>
            <label htmlFor='city_or_village'>City or Village:</label>
            <input id='city_or_village' type="text" {...register('city_or_village')} />
            {errors.city_or_village && <p>{errors.city_or_village.message}</p>}
          </div>
          <div className={styles.modal_buttons}>
            <button type="submit">{isEditing ? 'Update' : 'Save'}</button>
            <button type="button" onClick={onClose}>Cancel</button>
          </div>
        </form>
      </div>
    </Underlay>
  )
}

export default AddressModal