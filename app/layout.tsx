import type { Metadata } from "next"
import ReactQueryProvider from '@/src/providers/react-query-provider'
import Header from '@/src/components/header/Header'
import "./globals.scss"

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <ReactQueryProvider>
          <Header />
          {children}
        </ReactQueryProvider>
      </body>
    </html>
  )
}
