// // Frontend Implementation

// // First, install dependencies:
// // npm install braintree-web-drop-in-react

// import { useEffect, useState } from 'react'
// import DropIn from "braintree-web-drop-in-react"
// import <PERSON><PERSON> from '../../alert/Alert'


// interface Props {
//   orderId: number
//   amount: number
// }

// const BraintreeCheckout = ({ orderId, amount }: Props) => {
//   const [clientToken, setClientToken] = useState<string | null>(null)
//   const [instance, setInstance] = useState<any>(null)
//   const [loading, setLoading] = useState(false)
//   const [error, setError] = useState<string | null>(null)

//   useEffect(() => {
//     // Fetch client token when component mounts
//     const fetchClientToken = async () => {
//       try {
//         const response = await fetch('http://127.0.0.1:8000/api/payments/client-token/')
//         const data = await response.json()
//         setClientToken(data.client_token)
//       } catch (err) {
//         setError('Failed to initialize payment system')
//       }
//     }

//     fetchClientToken()
//   }, [])

//   const handlePayment = async () => {
//     if (!instance) {
//       return
//     }

//     setLoading(true)
//     setError(null)

//     try {
//       // Get payment method nonce
//       const { nonce } = await instance.requestPaymentMethod()

//       // Process payment
//       const response = await fetch('http://127.0.0.1:8000/api/payments/process-payment/', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           payment_method_nonce: nonce,
//           order_id: orderId,
//         }),
//       })

//       const data = await response.json()

//       if (data.success) {
//         // Payment successful - refresh page or redirect
//         window.location.reload()
//       } else {
//         setError(data.error || 'Payment failed')
//       }
//     } catch (err) {
//       setError('An error occurred while processing payment')
//     } finally {
//       setLoading(false)
//     }
//   }

//   if (!clientToken) {
//     return <div>Loading payment system...</div>
//   }

//   return (
//     <div className="w-full max-w-md mx-auto">
//       {error && (
//         // <Alert variant="error">
//         //   {error}
//         // </Alert>
//         <Alert variant='error' message={`Something went wrong`} />
//       )}

//       <DropIn
//         options={{
//           authorization: clientToken,
//           paypal: {
//             flow: 'checkout',
//             amount: amount,
//             currency: 'USD',
//           },
//           card: {
//             cardholderName: {
//               required: true
//             }
//           }
//         }}
//         onInstance={instance => setInstance(instance)}
//       />

//       <button
//         onClick={handlePayment}
//         disabled={loading || !instance}
//         className={`w-full py-2 px-4 bg-blue-600 text-white rounded-md
//           ${loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}`}
//       >
//         {loading ? 'Processing...' : `Pay $${amount}`}
//       </button>
//     </div>
//   )
// }

// export default BraintreeCheckout

// // # Server - side Implementation(Django)

// // # 1. First, install the PayPal SDK
// // # pip install paypal - checkout - serversdk

