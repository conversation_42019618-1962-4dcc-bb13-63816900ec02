'use client'

import React from 'react'
import Logo from '@/src/components/utils/logo/Logo'
import Alert from '@/src/components/utils/alert/Alert'
import { AxiosError } from 'axios'
import { ErrorResponse } from '@/src/types/types'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import styles from '@/app/(auth)/register/initiate/Initiate.module.scss'

interface RegisterLayoutProps {
  children: React.ReactNode;
  title: string;
  error: AxiosError<ErrorResponse> | null;
}

const RegisterLayout: React.FC<RegisterLayoutProps> = ({ children, title, error }) => {
  return (
    <div className={styles.register_container}>
      <div className={`${styles.form_container} container`}>
        <div className='logo_header'> 
          <Logo />
        </div>
        {error && <Alert variant="error" message={getErrorMessage(error)} />}
        <h2 className='title'>{title}</h2>
        {children}
      </div>
    </div>
  )
}

export default RegisterLayout