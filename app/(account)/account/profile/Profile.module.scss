@use '../../../../src/scss/variables' as *;
@use '../../../../src/scss/mixins' as *;

.profile {
  width: 100%;
  // background-color: aquamarine;
  // margin: 0 auto;
  // padding: 1rem;
  // border: 2px solid black;
  @include flexbox(flex-start, center, column);

  .user_details {
    width: 100%;
    // margin: 0 auto;
    // background-color: #ffbbef;
    max-width: 600px;
    // align-self: center;
  }

  h2 {
    // background-color: #ffbbef;
    padding: .5rem 0;
    font-size: 23px;
    text-align: center;
    font-weight: bold;
  }
}

.profile__details {
  @include flexbox(space-between, flex-start);
  padding: 1rem;

  div {
    h5 {
      font-weight: bold;
      color: $primary-dark-text-color;
      margin-bottom: 0.5rem;
      font-size: 17px;
    }

    p {
      color: $primary-lighter-text-color;
    }
  }
}

.profile__editable {
  @include flexbox(flex-start, flex-start, column);
  background-color: $sky-lighter-blue;
  padding: 1rem;
  border-radius: $border-radius-3;
  box-shadow: $box-shadow-1;

  div {
    margin-bottom: 1rem;
    width: 100%;

    label {
      font-weight: bold;
      color: $primary-dark-text-color;
      display: block;
      margin-bottom: 0.5rem;
    }

    input {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid $primary-lighter-text-color;
      border-radius: 3px;
      font-family: $primary-font-family;

      &:focus {
        outline: 1px solid $lighten-blue;
      }
    }

    p {
      color: $error-red;
      font-size: 14px;
      margin-top: 0.5rem;
    }
  }

  div:last-child {
    margin: 0 auto;
    @include flexbox(center, center);
    column-gap: 1rem;
  }
}

.edit_btn {
  @include btn($primary-blue, #fff);
  padding: .3rem 1.2rem;
  // font-weight: ;
  // text-transform: uppercase;
  border: 1px solid $lighten-blue;
  letter-spacing: .7px;
  transition: all 0.3s ease;
  height: fit-content;
  font-size: 14px;


  &:hover {
    // background-color: #fff;
    border: 1px solid $primary-dark-blue;
    color: $primary-dark-blue;
    // font-weight: bold;
  }
}

.edit_btn__empty {
  @include btn($lighten-blue, #fff);
  padding: .3rem 1.2rem;
  // font-weight: bold;
  // text-transform: uppercase;
  border: 1px solid $lighten-blue;
  letter-spacing: .7px;
  transition: all 0.3s ease;


  &:hover {
    border: 1px solid $primary-blue;
    color: $primary-blue;
    // font-weight: bold;
  }
}

//   .profile__details,
//   .profile__editable {
//     padding: 1rem;
//   }
// }

.reset_details {
  // @include flexbox(flex-start, flex-start, column);
  // display: flex;
  row-gap: 1rem;
  padding: 1rem;

  .reset_details__section {
    @include flexbox(space-between, center);

    div {
      h5 {
        font-weight: bold;
        color: $primary-dark-text-color;
        margin-bottom: 0.5rem;
        font-size: 17px;
      }

      p {
        color: $primary-lighter-text-color;
      }
    }

    .no_data {
      font-style: italic;
    }
  }
}

@media (width > $tablet) {

  .profile__details,
  .profile__editable {
    button {
      max-width: fit-content;
      align-self: center;
    }
  }
}