'use client'

import { useF<PERSON>, SubmitHandler } from 'react-hook-form'
import { useEffect, useState } from 'react'
import { z } from 'zod'
import { useRouter } from 'next/navigation'

import { zodResolver } from '@hookform/resolvers/zod'

import Modal from '@/src/components/utils/modal/Modal' // Importing the Modal component
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '@/src/types/types'
import { updateCustomerSchema } from '@/src/schemas/schemas'
import authStore from '@/src/stores/auth-store'
import loading_svg from '@/public/loading_svg_white.svg'
import styles from './CreateCustomer.module.scss'
import { useUpdateCustomer } from '@/src/hooks/customer-hooks'
import RegisterLayout from '../RegisterLayout'
import Alert from '@/src/components/utils/alert/Alert'

export type UpdateCustomerShape = z.infer<typeof updateCustomerSchema>

const UpdateCustomer = () => {
  const router = useRouter()
  const { customerDetailsSubmitted, setCustomerDetailsSubmitted } = authStore()
  const { mutation } = useUpdateCustomer()
  const [showUnderageModal, setShowUnderageModal] = useState(false) // State to control modal visibility

  const { register, handleSubmit, formState: { errors }, trigger } = useForm<UpdateCustomerShape>({
    resolver: zodResolver(updateCustomerSchema)
  })

  const handleUnderageClick = () => {
    // Show the modal when the button is clicked
    setShowUnderageModal(true)
  }

  const onSubmit: SubmitHandler<UpdateCustomerShape> = async (data) => {
    const isValid = await trigger('birth_date') // Manually trigger validation for birth_date

    if (!isValid) {
      // If birth_date validation fails, show the modal
      setShowUnderageModal(true)
      return
    }

    // If all validations pass, proceed with submitting the form
    mutation.mutate(data, {
      onSuccess: () => {
        // setUsername(null)
        // setPasswordSubmitted(true)
        setCustomerDetailsSubmitted(true)
        router.push('/register/update-info/')
      },
    })
  }

  useEffect(() => {
    if (customerDetailsSubmitted) {
      router.push('/register/update-info/')
    }
  }, [customerDetailsSubmitted, router])

  // useEffect(() => {
  //   if (!regInitiated) {
  //     router.push('/register/initiate/') // or whichever page you'd like to send them to
  //   }
  // }, [ router, regInitiated])

  const handleModalConfirm = () => {
    // Handle the case where the user confirms they are under 18
    console.log('User is under 18 but confirmed being underage.')
    setShowUnderageModal(false) // Close the modal
  }

  const handleModalClose = () => {
    // Handle the case where the user cancels and wants to correct their birth date
    setShowUnderageModal(false)
  }

  return (
    <RegisterLayout title="Let's update your profile" error={mutation.error as AxiosError<ErrorResponse>}>
      {mutation.isSuccess ? (
        <Alert variant="success" message='Customer updated successfully.' />
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <div className='form_group'>
            <label className='form_label' htmlFor="first_name">First Name:</label>
            <input className='form_input' type="text" id="first_name" {...register("first_name")} />
            {errors.first_name && <p className='form_error'>{errors.first_name.message} &#128543;</p>}
          </div>

          <div className='form_group'>
            <label className='form_label' htmlFor="last_name">Last Name:</label>
            <input className='form_input' type="text" id="last_name" {...register("last_name")} />
            {errors.last_name && <p className='form_error'>{errors.last_name.message} &#128543;</p>}
          </div>

          <div className='form_group'>
            <label className='form_label' htmlFor="birth_date">Date of birth:</label>
            <input className='form_input' type="date" id="birth_date" {...register("birth_date")} />
            {errors.birth_date && <p className='form_error'>{errors.birth_date.message} &#128543;</p>}
          </div>

          {/* Conditionally render the "I am not eighteen" button */}
          {errors.birth_date && (
            <button type="button" onClick={handleUnderageClick} className={styles.underageButton}>
              I am not eighteen or older
            </button>
          )}

          <button type="submit" disabled={mutation.isPending}>
            {mutation.isPending ? (
              // <img src={loading_svg} alt="Loading..." className='loading_svg' />
              'Updating...'
            ) : (
              'Update'
            )}
          </button>
        </form>
      )}

      {/* Modal for confirmation */}
      <Modal
        title="Underage Confirmation"
        message="Are you sure you are not 18 years or older?"
        show={showUnderageModal}
        onClose={handleModalClose}
        onConfirm={handleModalConfirm}
        btn1='I am sure'
        btn2="It's mistake"
      />
    </RegisterLayout>
  )
}

export default UpdateCustomer
