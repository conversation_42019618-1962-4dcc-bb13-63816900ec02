import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import authStore from '../stores/auth-store'
import APIClient from '../lib/api-client'
import { ErrorResponse, InitRegUserShape, VerificationCredentials } from '../types/types'
import { passwordSchemaFormInputs } from '@/app/(auth)/register/set-password/page'
import { VerifyContactShape } from '@/app/(auth)/register/update-info/VerifyAuthContact'
import { loginSchema } from '../schemas/schemas'
import z from 'zod'
import { LoginUserShape } from '@/app/(auth)/login/page'
import { CUSTOMER_DETAILS } from '../constants/constants'


type RegResponseShape = {
  message: string
  username: string
}

type UpdateAuthInfoResponseShape = {
  message: string
  username: string
}

export interface UpdateAuthInfoInitShape {
  email?: string
  phone_number?: string
}


// Register hooks
export const useRegister = () => {
  const { setUsername } = authStore()

  // In AuthClient Response has defined as: Request = Response 
  // If request data is different do not forget to specify the types here. 
  const apiClient = new APIClient<RegResponseShape, InitRegUserShape>(`/auth/register/initiate/`)

  const mutation = useMutation<RegResponseShape, AxiosError<ErrorResponse>, InitRegUserShape>({
    mutationFn: (data: InitRegUserShape) => apiClient.post(data),  // Here `data` is of type `RegisterUserShape`
    onSuccess: (data) => {
      console.log(data)
      setUsername(data.username)
    }
  })

  return { mutation }
}

export const useSendVerifyRegCredentials = () => {
  const apiClient = new APIClient('/auth/register/verify/')

  const mutation = useMutation({
    mutationFn: (data: VerificationCredentials) => apiClient.post(data)
  })

  return { mutation }
}

export const useSetPassword = () => {

  const apiClient = new APIClient(`/auth/register/set-password/`)

  const mutation = useMutation({
    mutationFn: (data: passwordSchemaFormInputs) => apiClient.post(data)
  })

  return { mutation }
}

// Update customer information hooks
export const useSendVerifyCode = () => {
  const apiClient = new APIClient('add-contact/verify/')

  const mutation = useMutation({
    mutationFn: (data: VerifyContactShape) => apiClient.post(data)
  })

  return { mutation }
}

export const useSendUpdateAuthInfo = () => {
  const apiClient = new APIClient<UpdateAuthInfoResponseShape, UpdateAuthInfoInitShape>('add-contact/initiate/')
  // const { setAltUsername } = authStore()

  const authInfoMutation = useMutation<UpdateAuthInfoResponseShape, AxiosError<ErrorResponse>, UpdateAuthInfoInitShape>({
    mutationFn: (data) => apiClient.patch(data),
    // onSuccess: (data) => {
    //   console.log(data)
    //   setAltUsername(data.username)
    // }
  })

  return { authInfoMutation }
}

export const useLogin = () => {
  const { setIsLoggedIn } = authStore()
  const apiClient = new APIClient(`/auth/login/`)

  const mutation = useMutation({
    mutationFn: (data: LoginUserShape) => apiClient.post(data),
    onSuccess: () => { // data is the response data
      // const { access } = data.data
      setIsLoggedIn(true)
    }
    // onSettled: (data, error, variables) => {
    //   console.log(data)
    //   console.log(error)
    //   console.log(variables) // variables are user input data
    // }
  })

  return { mutation }
}

export const useLogout = () => {
  const { setIsLoggedIn } = authStore()
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/auth/logout/`)

  const mutation = useMutation({
    mutationFn: () => apiClient.post(),
    onSuccess: () => {
      console.log('Logout was success')
      setIsLoggedIn(false)
      queryClient.invalidateQueries({
        queryKey: [CUSTOMER_DETAILS],
      })
      // queryClient.refetchQueries({
      //   queryKey: [CUSTOMER_DETAILS]
      // })
      queryClient.removeQueries({
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { mutation }
}





