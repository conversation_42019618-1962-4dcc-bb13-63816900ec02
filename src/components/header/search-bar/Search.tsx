import React, { useState, useEffect, useRef } from 'react'
import { IoMdSearch } from "react-icons/io"
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import styles from './Search.module.scss'
import useCategories from '@/src/hooks/product-hooks'

// Interface for the Category object
interface Category {
  id: number
  title: string
  slug: string
  level: number
  parent?: number | null  // The parent category, if applicable (can be null or undefined)
  children?: Category[]   // Nested children categories (optional)
}

const Search = () => {
  // State for the search input value
  const [searchValue, setSearchValue] = useState<string>("")

  // State to control the visibility of suggestions dropdown
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false)

  // State to hold the list of suggested categories (with their children)
  const [suggestedCategories, setSuggestedCategories] = useState<Category[]>([])

  // Navigation hook to programmatically navigate to different pages
  const router = useRouter()

  // Ref to the search container to detect clicks outside of it
  const searchRef = useRef<HTMLDivElement>(null)

  // Fetch all categories using the custom hook `useCategories`
  const { data: categories = [] } = useCategories() as { data: Category[] }

  // Recursive function to find all children for a given category
  const findAllChildrenRecursive = (categoryId: number, allCategories: Category[]): Category[] => {
    // Find all categories whose parent matches the given category ID
    const children = allCategories.filter(cat => cat.parent === categoryId)

    // For each child, recursively find its own children
    return children.map(child => ({
      ...child,
      children: findAllChildrenRecursive(child.id, allCategories)
    }))
  }

  // // useEffect hook to update the suggestions list based on the search input
  // useEffect(() => {
  //   // If the search value is not empty
  //   if (searchValue.trim()) {
  //     const lowerCaseSearchValue = searchValue.toLowerCase()

  //     // Filter categories based on the search value (case insensitive)
  //     const matchedCategories = categories.filter(category =>
  //       category.title.toLowerCase().includes(lowerCaseSearchValue)
  //     )

  //     // For each matched category, find its children recursively
  //     const categoriesWithChildren = matchedCategories.map(category => ({
  //       ...category,
  //       children: findAllChildrenRecursive(category.id, categories)
  //     }))

  //     // Update the suggested categories state
  //     setSuggestedCategories(categoriesWithChildren)
  //   } else {
  //     // If search value is empty, clear the suggestions
  //     setSuggestedCategories([])
  //   }
  // }, [searchValue, categories]) // Adding findAllChildrenRecursive will cause infinite loop

  useEffect(() => {
    if (searchValue.trim()) {
      const lowerCaseSearchValue = searchValue.toLowerCase()

      // Filter categories based on the search value
      const matchedCategories = categories.filter(category =>
        category.title.toLowerCase().includes(lowerCaseSearchValue)
      )

      // Find children recursively for matched categories
      const categoriesWithChildren = matchedCategories.map(category => ({
        ...category,
        children: findAllChildrenRecursive(category.id, categories)
      }))

      // Only update the state if the new suggestions differ from the current suggestions
      if (JSON.stringify(categoriesWithChildren) !== JSON.stringify(suggestedCategories)) {
        setSuggestedCategories(categoriesWithChildren)
      }
    } else {
      // Clear suggestions if searchValue is empty
      if (suggestedCategories.length > 0) {
        setSuggestedCategories([])
      }
    }
  }, [searchValue, categories])


  // useEffect hook to handle clicks outside the search component
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // If the click is outside the search box, close the suggestions dropdown
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    // Add event listener for mouse clicks
    document.addEventListener('mousedown', handleClickOutside)

    // Cleanup the event listener when the component is unmounted
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Handler for form submission (search submit)
  const handleSearchSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    // If the search value is valid (non-empty), navigate to the search results page
    if (searchValue.trim()) {
      router.push(`/products/?search=${searchValue}`)

      // Close the suggestions dropdown
      setShowSuggestions(false)
    }
  }

  // Render function for categories (including recursive rendering of children)
  const renderCategories = (category: Category) => (
    <div key={category.id} className={styles.category_item}>
      {/* Link to the category page */}
      <Link
        href={`/category/${category.slug}`}
        onClick={() => setShowSuggestions(false)}  // Close the dropdown when clicked
      >
        {category.title}
      </Link>

      {/* If the category has children, recursively render them */}
      {category.children && category.children.length > 0 && (
        <div className={styles.child_categories}>
          {category.children.map(child => renderCategories(child))}
        </div>
      )}
    </div>
  )

  return (
    <div className={styles.search} ref={searchRef}>
      {/* Form for the search input */}
      <form onSubmit={handleSearchSubmit}>
        <input
          type="text"
          placeholder="Search..."  // Input placeholder text
          value={searchValue}  // Bind input value to state
          onChange={(e) => setSearchValue(e.target.value)}  // Update state on input change
          onFocus={() => setShowSuggestions(true)}  // Show suggestions when input is focused
        />
        <button type="submit">
          <IoMdSearch />  {/* Search icon */}
        </button>
      </form>

      {/* If there are suggestions to show */}
      {showSuggestions && (
        <div className={styles.search_suggestions}>
          <div className={styles.backdrop} onClick={() => setShowSuggestions(false)}></div>  {/* Clickable backdrop to close dropdown */}

          <div className={styles.suggestions}>
            {/* Render the suggested categories or show a "No suggestions" message */}
            {suggestedCategories.length > 0 ? (
              suggestedCategories.map(category => renderCategories(category))
            ) : (
              <p className={styles.no_suggestions}>No suggestions found</p>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default Search
