import { useState } from "react"
import { FaAnglesDown } from "react-icons/fa6"
import NavigationCard from "./navigation-card/NavigationCard"
import styles from "./Navbar.module.scss"
import useCategories from "@/src/hooks/product-hooks"


const Navbar = () => {
  const { isPending, error, data: categories = [] } = useCategories()
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <section className={styles.navbar}>
        <div
          className={styles.navbar__links}
          onMouseOver={() => setIsOpen(true)}
          onMouseLeave={() => setIsOpen(false)}
        >
          <button >All Products</button>
          <i><FaAnglesDown /></i>
        </div>
      </section>
      {isOpen && (
        <NavigationCard
          isPending={isPending}
          error={error}
          categories={categories}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
        />
      )}
    </>
  )
}

export default Navbar