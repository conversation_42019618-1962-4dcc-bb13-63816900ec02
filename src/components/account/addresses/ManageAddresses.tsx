import { useState } from "react"
import { z } from "zod"
import AddressModal from "./address-modal/AddressModal"
import { FiTrash2 } from "react-icons/fi"
import { AxiosError } from "axios"
import { addressSchema } from "@/src/schemas/schemas"
import authStore from "@/src/stores/auth-store"
import { useCustomerDetails } from "@/src/hooks/customer-hooks"
import { useCreateAddress, useDeleteAddress, useUpdateAddress } from "@/src/hooks/address-hooks"
import { getErrorMessage } from "../../utils/getErrorMessage"
import { ErrorResponse } from "@/src/types/types"
import Alert from "../../utils/alert/Alert"
import styles from './ManageAddresses.module.scss'

export type AddressFormInputs = z.infer<typeof addressSchema>

interface Props {
  addresses: AddressFormInputs[]
}

const ManageAddresses = ({ addresses }: Props) => {
  const { isLoggedIn } = authStore()
  const { data: customerData } = useCustomerDetails(isLoggedIn)

  const [editingAddressIndex, setEditingAddressIndex] = useState<number | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Get the addressId based on the current editing index
  const editingAddressId = editingAddressIndex !== null ? addresses[editingAddressIndex]?.id : null

  // Pass the actual addressId to the useUpdateAddress hook, default to 0 if not editing
  const { updateAddress } = useUpdateAddress(editingAddressId ?? 0)
  const { deleteAddress } = useDeleteAddress()

  const { createAddress } = useCreateAddress()

  const handleEditAddress = (_address: AddressFormInputs, index: number) => {
    setEditingAddressIndex(index)
    setIsModalOpen(true)
  }

  const handleAddNewAddress = () => {
    console.log("handleAddNewAddress is called")
    setEditingAddressIndex(null)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingAddressIndex(null)
  }

  // Utility function to filter only modified fields
  const getChangedFields = (original: AddressFormInputs, updated: AddressFormInputs) => {
    return Object.keys(updated).reduce((changes, key) => {
      const k = key as keyof AddressFormInputs

      // Skip if both values are empty (undefined or empty string)
      const isEmptyValue = (val: unknown) => val === undefined || val === ""
      if (isEmptyValue(original[k]) && isEmptyValue(updated[k])) {
        return changes
      }

      // Only include if values are actually different
      if (updated[k] !== original[k]) {
        return { ...changes, [k]: updated[k] }
      }

      return changes
    }, {} as Partial<AddressFormInputs>)
  }

  const handleSubmitAddress = (addressDetails: AddressFormInputs) => {

    if (editingAddressIndex !== null) {
      const originalAddress = addresses[editingAddressIndex]
      const patchData = getChangedFields(originalAddress, addressDetails) // Get only changed fields

      if (Object.keys(patchData).length > 0) { // Check if there are any changes
        updateAddress.mutate(patchData, {
          onSuccess: () => {
            setIsModalOpen(false)
            setEditingAddressIndex(null)
          },
          onError: (error) => {
            console.error('Error updating address:', error)
          }
        })
      } else {
        console.log('No changes detected, skipping the update.')
      }
    } else {
      // If adding a new address, send all fields as it's a full data submission
      const newAddressData = { ...addressDetails, customer: customerData!.id }
      createAddress.mutate(newAddressData, {
        onSuccess: () => {
          setIsModalOpen(false)
        },
        onError: (error) => {
          console.error('Error adding address:', error)
        }
      })
    }
  }

  const handleDeleteAddress = (addressId: number) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this address?')
    if (confirmDelete) {
      deleteAddress.mutate(addressId, {
        onSuccess: () => {
          // Optionally handle post-delete success actions
          console.log('Address deleted successfully')
        },
        onError: (error) => {
          console.error('Error deleting address:', error)
        }
      })
    }
  }

  return (
    <>
      <h3 className={styles.title}>Shipping Addresses</h3>

      {/* Update Address Error Handling */}
      {updateAddress.error && <Alert
        variant="error"
        message={getErrorMessage(updateAddress.error as AxiosError<ErrorResponse>)}
        textAlign='center'
      />}

      {/* Delete Address Error Handling */}
      {deleteAddress.error && <Alert
        variant="error"
        message={getErrorMessage(deleteAddress.error as AxiosError<ErrorResponse>)}
        textAlign='center'
      />}

      <div className={styles.addresses}>
        {addresses?.map((address: AddressFormInputs, index: number) => (
          <div key={address.id} className={styles.addresses__list}>
            <div>
              {/* <h5>Full Name:</h5> */}
              <p>{address.full_name}</p>
            </div>
            <div>
              {/* <h5>Street Name:</h5> */}
              <p>{address.street_name}</p>
            </div>
            {address.address_line_1 && (
              <div>
                {/* <h5>Address Line 1:</h5> */}
                <p>{address.address_line_1}</p>
              </div>
            )}
            {address.address_line_2 && (
              <div>
                {/* <h5>Address Line 2:</h5> */}
                <p>{address.address_line_2}</p>
              </div>
            )}
            <div>
              {/* <h5>Postal Code:</h5> */}
              <p>{address.postal_code}</p>
            </div>
            <div>
              {/* <h5>City or Village:</h5> */}
              <p>{address.city_or_village}</p>
            </div>
            <div className={styles.addresses__list__edit_buttons}>
              <button
                type="button"
                onClick={() => handleEditAddress(address, index)}
                disabled={updateAddress.isPending || deleteAddress.isPending}
              >
                Edit
              </button>
              <button
                type="button"
                onClick={() => handleDeleteAddress(address.id!)}
                disabled={updateAddress.isPending || deleteAddress.isPending}
              >
                <i><FiTrash2 /></i>
              </button>
            </div>
          </div>
        ))}
      </div>
      <button
        type="button"
        onClick={handleAddNewAddress}
        className="empty_btn"
        disabled={updateAddress.isPending || deleteAddress.isPending}
      >
        + Add a new address
      </button>
      {isModalOpen && (
        <AddressModal
          onSubmit={handleSubmitAddress}
          onClose={handleCloseModal}
          initialData={editingAddressIndex !== null ? addresses[editingAddressIndex] : undefined}
          isEditing={editingAddressIndex !== null}
          isOpen={isModalOpen}
        />
      )}
    </>
  )
}

export default ManageAddresses