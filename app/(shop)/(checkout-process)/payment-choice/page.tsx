'use client'

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import loading_svg from '../../../assets/loading_svg_white.svg'
import CheckoutLayout from "../CheckoutLayout"
import authStore from "@/src/stores/auth-store"
import { useCustomerDetails } from "@/src/hooks/customer-hooks"
import cartStore from "@/src/stores/cart-store"
import { useCart } from "@/src/hooks/cart-hooks"
import { useCreateOrder } from "@/src/hooks/order-hooks"
import { PaymentOptionsShape } from "@/src/types/types"
import EmptyCart from "@/src/components/utils/empty-cart/EmptyCart"
import Spinner from "@/src/components/utils/spinner/Spinner"
import Alert from "@/src/components/utils/alert/Alert"
import Link from "next/link"
import { usePaymentOptions } from "@/src/hooks/checkout-hooks"


const PaymentChoice = () => {
  const router = useRouter()
  const { isLoggedIn } = authStore()
  const { data: customer } = useCustomerDetails(isLoggedIn)
  const { cartId, setSelectedPaymentOption, selectedAddress, selectedPaymentOption } = cartStore()
  const { isPending, error, data } = useCart()

  const { createOrder } = useCreateOrder()
  const payOptions = usePaymentOptions()

  console.log(payOptions.data)

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/user/login')
    }
  }, [isLoggedIn, router])

  const handlePaymentOptionChange = (paymentOption: PaymentOptionsShape) => {
    setSelectedPaymentOption(paymentOption)
  }

  useEffect(() => {
    if (payOptions.data && payOptions.data.length > 0 && !selectedPaymentOption) {
      setSelectedPaymentOption(payOptions.data[0])
    }
  }, [payOptions.data, selectedPaymentOption, setSelectedPaymentOption])

  console.log(customer)
  console.log(selectedAddress)
  console.log(selectedPaymentOption)

  const createOrderFn = () => {
    if (window.confirm("Payment Method or other order details cannot be changed after placing the order.\n" +
      "Make sure you have selected the correct payment method and other order details before placing the order.")) {
      if (customer?.id && selectedAddress?.id && selectedPaymentOption?.id) {
        createOrder.mutate({
          cart_id: cartId!,
          delivery_status: "Pending",
          selected_address: selectedAddress.id,
          payment_method: selectedPaymentOption.id
        })
      }
    }
  }

  return (
    <>
      {!cartId ? <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' /> :
        isPending ? <Spinner color='#0091CF' size={20} loading={true} /> :
          error ? <Alert variant="error" message={error.message} /> :
            (!data || data?.cart_items?.length === 0 || Object.keys(data).length === 0) ? (
              <div>
                <p>Your cart is empty. Add some products to the cart to checkout!</p>
                <Link href='/'>Go Shopping </Link>
              </div>
            ) : (
              <CheckoutLayout
                cartItems={data.cart_items}
                totalPrice={data?.total_price}
                shippingCost={data?.shipping_cost}
                grandTotal={data?.grand_total}
                cartWeight={data?.cart_weight}
                showCartItems={true}
                showPriceSummary={true}
                showLogo={true}
              >
                <div>
                  <h3>Place Order</h3>
                  <div>
                    <section>
                      <section>
                        <div>
                          <h3>Contact Details: </h3>
                          <p>Deliver to: {customer?.first_name} {customer?.last_name}</p>
                          <p>Phone: {customer?.phone_number}</p>
                          <p>Email to: {customer?.email}</p>
                        </div>
                        <div>
                          <h3>Shipping address: </h3>
                          <address>
                            {selectedAddress?.full_name},<br />
                            {selectedAddress?.street_name},<br />
                            {selectedAddress?.postal_code},<br />
                            {selectedAddress?.city_or_village}<br />
                          </address>
                        </div >
                      </section>
                      <hr />
                      <section>
                        <div>
                          <h3>Payment Method:</h3>
                          {payOptions?.isPending ? <Alert variant="info" message="Payment options are loading" /> :
                            payOptions?.error ? <Alert variant="error" message={payOptions.error.message} /> :
                              payOptions?.data?.length === 0 ? <Alert variant="error" message="No payment options available" /> :
                                <>
                                  {payOptions?.data?.map((option: PaymentOptionsShape) => (
                                    <div key={option.id}>
                                      <input
                                        type="radio"
                                        id={`payment-${option.id}`}
                                        name="payment-option"
                                        checked={selectedPaymentOption?.id === option.id}
                                        onChange={() => handlePaymentOptionChange(option)}
                                      />
                                      <label htmlFor={`payment-${option.id}`}>{option.name}</label>
                                    </div>
                                  ))}
                                </>}
                        </div>
                      </section>
                    </section >
                    <div>
                      <button type="submit" disabled={createOrder.isPending} onClick={createOrderFn}>
                        {createOrder.isPending ? (
                          // <img src={loading_svg} alt="Loading..." className='loading_svg' />
                          'Processing...'
                        ) : (
                          'Place Order'
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </CheckoutLayout>
            )}
    </ >
  )
}


export default PaymentChoice