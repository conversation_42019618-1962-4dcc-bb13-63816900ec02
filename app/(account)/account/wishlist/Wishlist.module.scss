@use "../../../../src/scss/variables" as *;
@use "../../../../src/scss/mixins" as *;

.wishlist {
  width: 100%;
  padding: 0 $padding-4 $padding-4;
  background-color: $info-bg;
  box-shadow: $box-shadow-1;
  max-width: 800px;
  margin: auto;

  .heading {
    padding: .5rem 0;
    font-size: 23px;
    text-align: center;
    font-weight: bold;
  }

  .wishlist__items {
    @include flexbox(flex-start, flex-start, column);
    gap: $padding-3;

    .wishlist__item {
      display: grid;
      grid-template-columns: 20% 80%;
      gap: 1rem;
      padding: 1rem;
      background-color: white;
      box-shadow: $box-shadow-1;
      position: relative;

      &.inactive {
        opacity: 0.6;
      }

      .product__image__container {
        @include flexbox(center, center);
        position: relative;

        .inactive__overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          @include flexbox(center, center);
          z-index: 10;
        }

        .inactive__text {
          color: white;
          font-weight: bold;
          text-align: center;
          padding: 0.5rem;
          background-color: rgba(0, 0, 0, 0.7);
          border-radius: 4px;
        }

        .product__image {
          object-fit: contain;
        }
      }

      .item__details {
        @include flexbox(flex-start, flex-start, column);
        gap: $padding-1;
        position: relative;

        .product__title {
          font-size: $font-size-3;
          color: $primary-dark-text-color;
          font-weight: bold;
        }

        .product__price {
          font-size: $font-size-2;
          color: $primary-green;
          font-weight: bold;
        }

        .added__date {
          font-size: $font-size-1;
          color: $primary-lighter-text-color;
        }

        .product__link {
          text-decoration: none;
          color: $primary-blue;
          font-size: $font-size-2;
          font-weight: bold;

          &:hover {
            text-decoration: underline;
          }

          &.disabled {
            color: $primary-lighter-text-color;
            pointer-events: none;
          }
        }

        .delete__button {
          @include btn($error-red, #fff);
          margin: 0 3px 0 0;
          position: absolute;
          bottom: -10px;
          right: 0;
          padding: .6rem;
          border-radius: 50%;
          transition: all 0.3s ease;

          &:hover {
            background-color: lighten($error-red, 40%);
          }

          &:active {
            color: darken($warning-text, 10%);
          }
        }
      }
    }
  }

  .emptyMessage {
    text-align: center;
    color: $primary-dark-text-color;
    font-size: $font-size-3;
  }

  .loader,
  .error {
    text-align: center;
    font-size: $font-size-3;
    color: $warning-text;
  }
}